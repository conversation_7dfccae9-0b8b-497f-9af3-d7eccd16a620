{% extends "base.html" %}

{% block title %}Edit Profile - Amazora{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <div class="bg-white rounded-xl shadow-lg p-6">
        <div class="mb-6">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Edit Profile</h1>
            <p class="text-gray-600">Update your personal information and preferences.</p>
        </div>

        <form method="POST" enctype="multipart/form-data" class="space-y-6">
            <!-- Avatar Upload -->
            <div>
                <label class="block text-sm font-medium text-gray-700 mb-2">Profile Picture</label>
                <div class="flex items-center space-x-6">
                    <div class="w-24 h-24 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden">
                        {% if profile.avatar_url %}
                            <img src="{{ profile.avatar_url | image_url }}" alt="Avatar" class="w-full h-full object-cover" id="current-avatar">
                        {% else %}
                            <i class="fas fa-user text-white text-3xl" id="default-avatar"></i>
                        {% endif %}
                    </div>
                    <div>
                        <label for="avatar_file" class="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors duration-200">
                            <i class="fas fa-upload mr-2"></i>
                            Change Picture
                        </label>
                        <input id="avatar_file" name="avatar_file" type="file" accept="image/*" class="hidden" onchange="previewAvatar(this)">
                        <p class="text-xs text-gray-500 mt-1">PNG, JPG, JPEG, GIF or WebP (MAX. 16MB)</p>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                    <input type="text" 
                           id="first_name" 
                           name="first_name" 
                           value="{{ profile.first_name or '' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="Enter your first name">
                </div>

                <div>
                    <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                    <input type="text" 
                           id="last_name" 
                           name="last_name" 
                           value="{{ profile.last_name or '' }}"
                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                           placeholder="Enter your last name">
                </div>
            </div>

            <div>
                <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                <input type="tel" 
                       id="phone" 
                       name="phone" 
                       value="{{ profile.phone or '' }}"
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                       placeholder="Enter your phone number">
            </div>

            <!-- Address Information -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Address Information</h3>
                
                <div>
                    <label for="address" class="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                    <textarea id="address" 
                              name="address" 
                              rows="3"
                              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                              placeholder="Enter your street address">{{ profile.address or '' }}</textarea>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mt-6">
                    <div>
                        <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                        <input type="text" 
                               id="city" 
                               name="city" 
                               value="{{ profile.city or '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               placeholder="City">
                    </div>

                    <div>
                        <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State/Province</label>
                        <input type="text" 
                               id="state" 
                               name="state" 
                               value="{{ profile.state or '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               placeholder="State">
                    </div>

                    <div>
                        <label for="zip_code" class="block text-sm font-medium text-gray-700 mb-2">ZIP/Postal Code</label>
                        <input type="text" 
                               id="zip_code" 
                               name="zip_code" 
                               value="{{ profile.zip_code or '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               placeholder="ZIP Code">
                    </div>

                    <div>
                        <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                        <input type="text" 
                               id="country" 
                               name="country" 
                               value="{{ profile.country or '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               placeholder="Country">
                    </div>
                </div>
            </div>

            <!-- Account Information -->
            <div class="border-t border-gray-200 pt-6">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">Account Information</h3>
                <div class="bg-gray-50 rounded-lg p-4">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Username</label>
                            <p class="text-gray-900 font-medium">{{ current_user.username }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Email</label>
                            <p class="text-gray-900 font-medium">{{ current_user.email }}</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-500 mt-2">
                        <i class="fas fa-info-circle mr-1"></i>
                        Contact support to change your username or email address.
                    </p>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ url_for('user_profile') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Profile
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
                    <i class="fas fa-save mr-2"></i>
                    Save Changes
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function previewAvatar(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];
        const reader = new FileReader();
        
        reader.onload = function(e) {
            const currentAvatar = document.getElementById('current-avatar');
            const defaultAvatar = document.getElementById('default-avatar');
            
            if (currentAvatar) {
                currentAvatar.src = e.target.result;
            } else if (defaultAvatar) {
                // Replace the icon with an image
                defaultAvatar.parentElement.innerHTML = `<img src="${e.target.result}" alt="Avatar" class="w-full h-full object-cover" id="current-avatar">`;
            }
        };
        
        reader.readAsDataURL(file);
    }
}
</script>
{% endblock %}
