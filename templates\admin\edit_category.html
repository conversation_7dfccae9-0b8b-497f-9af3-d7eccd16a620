{% extends "admin/base.html" %}

{% block title %}Edit Category - Admin Dashboard{% endblock %}
{% block page_title %}Edit Category: {{ category.name }}{% endblock %}

{% block content %}
<div class="max-w-2xl">
    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Category Information</h3>
            <p class="text-gray-600">Update the category details below.</p>
        </div>

        <form method="POST" class="space-y-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name *
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       value="{{ category.name }}"
                       required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                       placeholder="Enter category name">
            </div>

            <div>
                <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                    URL Slug *
                </label>
                <input type="text" 
                       id="slug" 
                       name="slug" 
                       value="{{ category.slug }}"
                       required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                       placeholder="category-url-slug">
                <p class="text-sm text-gray-500 mt-1">This will be used in the URL. Use lowercase letters, numbers, and hyphens only.</p>
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="Enter category description (optional)">{{ category.description or '' }}</textarea>
            </div>

            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <div class="flex items-center">
                    <i class="fas fa-info-circle text-blue-600 mr-2"></i>
                    <span class="text-sm font-medium text-blue-800">Category Statistics</span>
                </div>
                <div class="mt-2 text-sm text-blue-700">
                    This category currently has <strong>{{ category.products|length }}</strong> product(s) associated with it.
                </div>
            </div>

            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ url_for('admin_categories') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
                    <i class="fas fa-save mr-2"></i>
                    Update Category
                </button>
            </div>
        </form>
    </div>
</div>
{% endblock %}
