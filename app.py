from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify
from flask_sqlalchemy import SQLAlchemy
from flask_login import Lo<PERSON><PERSON>anager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import json
from functools import wraps
import time
import uuid
from email_service import init_email_service, send_welcome_email, send_order_confirmation_email

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///amazora.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Performance optimizations
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # 1 year for static files

# Initialize email service
init_email_service(app)

# Allowed file extensions
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'webp'}

def allowed_file(filename):
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file):
    """Save uploaded file and return the filename"""
    if file and allowed_file(file.filename):
        # Create uploads directory if it doesn't exist
        upload_dir = os.path.join(app.static_folder, 'uploads')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        filename = secure_filename(file.filename)
        name, ext = os.path.splitext(filename)
        unique_filename = f"{name}_{uuid.uuid4().hex[:8]}{ext}"

        # Save file
        file_path = os.path.join(upload_dir, unique_filename)
        file.save(file_path)

        return f"uploads/{unique_filename}"
    return None

@app.template_filter('image_url')
def image_url_filter(image_path):
    """Template filter to handle both uploaded files and external URLs"""
    if not image_path:
        return None
    if image_path.startswith(('http://', 'https://')):
        return image_path  # External URL
    else:
        return url_for('static', filename=image_path)  # Local file

# Simple in-memory cache
cache_store = {}
cache_timeout = 300  # 5 minutes

# Add cache headers and compression for static files
@app.after_request
def add_cache_headers(response):
    # Cache static files for 1 year
    if request.endpoint == 'static':
        response.cache_control.max_age = 31536000
        response.cache_control.public = True
        # Add compression headers for text files
        if any(request.path.endswith(ext) for ext in ['.css', '.js', '.html', '.svg']):
            response.headers['Vary'] = 'Accept-Encoding'
    # Cache API responses for shorter time
    elif request.endpoint in ['home', 'category', 'product']:
        response.cache_control.max_age = 0  # Disable caching temporarily for debugging
        response.cache_control.no_cache = True
        response.cache_control.must_revalidate = True

    # Security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'
    response.headers['X-XSS-Protection'] = '1; mode=block'

    return response

db = SQLAlchemy(app)
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'

# Simple caching decorator
def cache_page(timeout=300):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # Create cache key from function name and arguments
            cache_key = f"{f.__name__}_{hash(str(args) + str(kwargs))}"

            # Check if cached result exists and is still valid
            if cache_key in cache_store:
                cached_data, timestamp = cache_store[cache_key]
                if time.time() - timestamp < timeout:
                    return cached_data

            # Generate new result and cache it
            result = f(*args, **kwargs)
            cache_store[cache_key] = (result, time.time())

            # Clean old cache entries (simple cleanup)
            current_time = time.time()
            keys_to_remove = [k for k, (_, ts) in cache_store.items() if current_time - ts > timeout]
            for k in keys_to_remove:
                del cache_store[k]

            return result
        return decorated_function
    return decorator

# Models
class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(120), nullable=False)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

class Category(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False, index=True)
    slug = db.Column(db.String(100), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    products = db.relationship('Product', backref='category', lazy=True)

class Product(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, index=True)
    slug = db.Column(db.String(200), unique=True, nullable=False, index=True)
    description = db.Column(db.Text)
    price = db.Column(db.Float, nullable=False, index=True)
    image_url = db.Column(db.String(200))
    stock = db.Column(db.Integer, default=0)
    category_id = db.Column(db.Integer, db.ForeignKey('category.id'), nullable=False, index=True)
    featured = db.Column(db.Boolean, default=False, index=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)

class CartItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, default=1)
    product = db.relationship('Product', backref='cart_items')

class Order(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    total_amount = db.Column(db.Float, nullable=False)
    status = db.Column(db.String(50), nullable=False, default='pending')  # pending, confirmed, shipped, delivered, cancelled
    payment_method = db.Column(db.String(50), nullable=False, default='instapay')  # instapay, pay_after_shipping
    shipping_address = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user = db.relationship('User', backref='orders')

class OrderItem(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    order_id = db.Column(db.Integer, db.ForeignKey('order.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    quantity = db.Column(db.Integer, nullable=False)
    price = db.Column(db.Float, nullable=False)  # Price at time of order
    order = db.relationship('Order', backref='items')
    product = db.relationship('Product', backref='order_items')

class Review(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    product_id = db.Column(db.Integer, db.ForeignKey('product.id'), nullable=False)
    rating = db.Column(db.Integer, nullable=False)  # 1-5 stars
    comment = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow, index=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    user = db.relationship('User', backref='reviews')
    product = db.relationship('Product', backref='reviews')

class UserProfile(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False, unique=True)
    first_name = db.Column(db.String(100))
    last_name = db.Column(db.String(100))
    phone = db.Column(db.String(20))
    address = db.Column(db.Text)
    city = db.Column(db.String(100))
    state = db.Column(db.String(100))
    zip_code = db.Column(db.String(20))
    country = db.Column(db.String(100))
    date_of_birth = db.Column(db.Date)
    avatar_url = db.Column(db.String(200))
    user = db.relationship('User', backref=db.backref('profile', uselist=False))
    user = db.relationship('User', backref='cart_items')

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

# Routes
@app.route('/')
@cache_page(timeout=180)  # Cache for 3 minutes
def home():
    # Optimized queries with eager loading
    featured_products = Product.query.options(db.joinedload(Product.category)).filter_by(featured=True).limit(8).all()
    categories = Category.query.all()

    # Debug: Print product image URLs
    print("DEBUG: Featured products:")
    for product in featured_products:
        print(f"  {product.name}: {product.image_url}")

    return render_template('home.html', featured_products=featured_products, categories=categories)

@app.route('/category/<slug>')
@cache_page(timeout=300)  # Cache for 5 minutes
def category(slug):
    category = Category.query.filter_by(slug=slug).first_or_404()
    # Optimized query - no need for eager loading here since we already have category
    products = Product.query.filter_by(category_id=category.id).all()
    return render_template('category.html', category=category, products=products)

@app.route('/product/<slug>')
def product(slug):
    # Optimized query with eager loading
    product = Product.query.options(db.joinedload(Product.category)).filter_by(slug=slug).first_or_404()
    related_products = Product.query.filter_by(category_id=product.category_id).filter(Product.id != product.id).limit(4).all()

    # Get reviews for this product
    reviews = Review.query.filter_by(product_id=product.id).order_by(Review.created_at.desc()).all()
    print(f"Product {product.id} has {len(reviews)} reviews")

    # Calculate average rating
    avg_rating = 0
    if reviews:
        avg_rating = sum(review.rating for review in reviews) / len(reviews)
        print(f"Average rating: {avg_rating}")

    # Check if current user has reviewed this product
    user_review = None
    if current_user.is_authenticated:
        user_review = Review.query.filter_by(user_id=current_user.id, product_id=product.id).first()

    # Calculate quantity range for the template
    max_quantity = min(product.stock + 1, 11) if product.stock > 0 else 1

    return render_template('product.html',
                         product=product,
                         related_products=related_products,
                         reviews=reviews,
                         avg_rating=avg_rating,
                         user_review=user_review,
                         max_quantity=max_quantity)

@app.route('/cart')
def cart():
    if current_user.is_authenticated:
        cart_items = CartItem.query.filter_by(user_id=current_user.id).all()
        total = sum(item.product.price * item.quantity for item in cart_items)
        return render_template('cart.html', cart_items=cart_items, total=total)
    else:
        # Handle session-based cart for non-authenticated users
        cart = session.get('cart', {})
        cart_items = []
        total = 0
        for product_id, quantity in cart.items():
            product = Product.query.get(int(product_id))
            if product:
                cart_items.append({'product': product, 'quantity': quantity})
                total += product.price * quantity
        return render_template('cart.html', cart_items=cart_items, total=total)

@app.route('/api/cart-count')
def cart_count():
    if current_user.is_authenticated:
        count = CartItem.query.filter_by(user_id=current_user.id).count()
    else:
        cart = session.get('cart', {})
        count = len(cart)
    return jsonify({'count': count})

@app.route('/add_to_cart', methods=['POST'])
def add_to_cart():
    product_id = request.json.get('product_id')
    quantity = request.json.get('quantity', 1)
    
    if current_user.is_authenticated:
        cart_item = CartItem.query.filter_by(user_id=current_user.id, product_id=product_id).first()
        if cart_item:
            cart_item.quantity += quantity
        else:
            cart_item = CartItem(user_id=current_user.id, product_id=product_id, quantity=quantity)
            db.session.add(cart_item)
        db.session.commit()
    else:
        # Handle session-based cart
        cart = session.get('cart', {})
        cart[str(product_id)] = cart.get(str(product_id), 0) + quantity
        session['cart'] = cart
    
    return jsonify({'success': True, 'message': 'Product added to cart'})

@app.route('/update_cart', methods=['POST'])
@login_required
def update_cart():
    cart_item_id = request.json.get('cart_item_id')
    quantity = request.json.get('quantity', 1)
    
    cart_item = CartItem.query.filter_by(id=cart_item_id, user_id=current_user.id).first()
    if cart_item:
        if quantity <= 0:
            db.session.delete(cart_item)
        else:
            cart_item.quantity = quantity
        db.session.commit()
        return jsonify({'success': True, 'message': 'Cart updated'})
    
    return jsonify({'success': False, 'message': 'Cart item not found'})

@app.route('/remove_from_cart', methods=['POST'])
@login_required
def remove_from_cart():
    cart_item_id = request.json.get('cart_item_id')
    
    cart_item = CartItem.query.filter_by(id=cart_item_id, user_id=current_user.id).first()
    if cart_item:
        db.session.delete(cart_item)
        db.session.commit()
        return jsonify({'success': True, 'message': 'Item removed from cart'})
    
    return jsonify({'success': False, 'message': 'Cart item not found'})

@app.route('/register', methods=['GET', 'POST'])
def register():
    if request.method == 'POST':
        username = request.form['username']
        email = request.form['email']
        password = request.form['password']
        
        if User.query.filter_by(username=username).first():
            flash('Username already exists')
            return redirect(url_for('register'))
        
        if User.query.filter_by(email=email).first():
            flash('Email already exists')
            return redirect(url_for('register'))
        
        user = User(
            username=username,
            email=email,
            password_hash=generate_password_hash(password)
        )
        db.session.add(user)
        db.session.commit()
        
        # Send welcome email
        try:
            send_welcome_email(user)
            print(f"Welcome email sent to {user.email}")
        except Exception as e:
            print(f"Failed to send welcome email: {e}")
        
        login_user(user)
        return redirect(url_for('home'))
    
    return render_template('register.html')

@app.route('/login', methods=['GET', 'POST'])
def login():
    if request.method == 'POST':
        username = request.form['username']
        password = request.form['password']

        # Check for admin credentials
        if username == 'administrator' and password == 'admin':
            # Create or get admin user
            admin_user = User.query.filter_by(username='administrator').first()
            if not admin_user:
                admin_user = User(
                    username='administrator',
                    email='<EMAIL>',
                    password_hash=generate_password_hash('admin'),
                    is_admin=True
                )
                db.session.add(admin_user)
                db.session.commit()
            else:
                admin_user.is_admin = True
                db.session.commit()

            login_user(admin_user)
            return redirect(url_for('admin_dashboard'))

        # Regular user login
        user = User.query.filter_by(username=username).first()

        if user and check_password_hash(user.password_hash, password):
            login_user(user)
            if user.is_admin:
                return redirect(url_for('admin_dashboard'))
            return redirect(url_for('home'))
        else:
            flash('Invalid username or password')

    return render_template('login.html')

@app.route('/logout')
@login_required
def logout():
    logout_user()
    return redirect(url_for('home'))

# Admin Dashboard Routes
@app.route('/admin')
@login_required
def admin_dashboard():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    # Dashboard statistics
    total_users = User.query.count()
    total_products = Product.query.count()
    total_categories = Category.query.count()
    total_orders = Order.query.count()

    # Recent data
    recent_users = User.query.order_by(User.created_at.desc()).limit(5).all()
    recent_products = Product.query.order_by(Product.created_at.desc()).limit(5).all()

    return render_template('admin/dashboard.html',
                         total_users=total_users,
                         total_products=total_products,
                         total_categories=total_categories,
                         total_orders=total_orders,
                         recent_users=recent_users,
                         recent_products=recent_products)

# Admin Orders Management
@app.route('/admin/orders')
@login_required
def admin_orders():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    orders = (Order.query
              .options(db.joinedload(Order.user), db.joinedload(Order.items).joinedload(OrderItem.product))
              .order_by(Order.created_at.desc())
              .all())

    return render_template('admin/orders.html', orders=orders)


@app.route('/admin/orders/<int:order_id>')
@login_required
def admin_order_detail(order_id: int):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    order = (Order.query
             .options(db.joinedload(Order.user), db.joinedload(Order.items).joinedload(OrderItem.product))
             .get_or_404(order_id))

    # Fetch user profile for phone and address details
    user_profile = UserProfile.query.filter_by(user_id=order.user_id).first()

    return render_template('admin/order_detail.html', order=order, user_profile=user_profile)

# Admin Users Management
@app.route('/admin/users')
@login_required
def admin_users():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    users = User.query.all()
    return render_template('admin/users.html', users=users)

@app.route('/admin/users/delete/<int:user_id>', methods=['POST'])
@login_required
def admin_delete_user(user_id):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    user = User.query.get_or_404(user_id)
    if user.id == current_user.id:
        flash('Cannot delete your own account')
        return redirect(url_for('admin_users'))

    db.session.delete(user)
    db.session.commit()
    flash('User deleted successfully')
    return redirect(url_for('admin_users'))

# Admin Categories Management
@app.route('/admin/categories')
@login_required
def admin_categories():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    categories = Category.query.all()
    total_products = sum(len(category.products) for category in categories)
    avg_products = total_products / len(categories) if categories else 0

    return render_template('admin/categories.html',
                         categories=categories,
                         total_products=total_products,
                         avg_products=avg_products)

@app.route('/admin/categories/add', methods=['GET', 'POST'])
@login_required
def admin_add_category():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    if request.method == 'POST':
        name = request.form['name']
        slug = request.form['slug']
        description = request.form['description']

        category = Category(name=name, slug=slug, description=description)
        db.session.add(category)
        db.session.commit()
        flash('Category added successfully')
        return redirect(url_for('admin_categories'))

    return render_template('admin/add_category.html')

@app.route('/admin/categories/edit/<int:category_id>', methods=['GET', 'POST'])
@login_required
def admin_edit_category(category_id):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    category = Category.query.get_or_404(category_id)

    if request.method == 'POST':
        category.name = request.form['name']
        category.slug = request.form['slug']
        category.description = request.form['description']
        db.session.commit()
        flash('Category updated successfully')
        return redirect(url_for('admin_categories'))

    return render_template('admin/edit_category.html', category=category)

@app.route('/admin/categories/delete/<int:category_id>', methods=['POST'])
@login_required
def admin_delete_category(category_id):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    category = Category.query.get_or_404(category_id)
    db.session.delete(category)
    db.session.commit()
    flash('Category deleted successfully')
    return redirect(url_for('admin_categories'))

# Admin Products Management
@app.route('/admin/products')
@login_required
def admin_products():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    products = Product.query.options(db.joinedload(Product.category)).all()
    return render_template('admin/products.html', products=products)

@app.route('/admin/products/add', methods=['GET', 'POST'])
@login_required
def admin_add_product():
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    if request.method == 'POST':
        name = request.form['name']
        slug = request.form['slug']
        description = request.form['description']
        price = float(request.form['price'])
        stock = int(request.form['stock'])
        category_id = int(request.form['category_id'])
        featured = 'featured' in request.form

        # Handle file upload
        image_url = None
        if 'image_file' in request.files:
            file = request.files['image_file']
            if file.filename != '':
                image_url = save_uploaded_file(file)
                if not image_url:
                    flash('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or WebP files.')
                    categories = Category.query.all()
                    return render_template('admin/add_product.html', categories=categories)

        product = Product(
            name=name,
            slug=slug,
            description=description,
            price=price,
            image_url=image_url,
            stock=stock,
            category_id=category_id,
            featured=featured
        )
        db.session.add(product)
        db.session.commit()

        # Clear cache when product is added
        cache_store.clear()

        flash('Product added successfully')
        return redirect(url_for('admin_products'))

    categories = Category.query.all()
    return render_template('admin/add_product.html', categories=categories)

@app.route('/admin/products/edit/<int:product_id>', methods=['GET', 'POST'])
@login_required
def admin_edit_product(product_id):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    product = Product.query.get_or_404(product_id)

    if request.method == 'POST':
        product.name = request.form['name']
        product.slug = request.form['slug']
        product.description = request.form['description']
        product.price = float(request.form['price'])
        product.stock = int(request.form['stock'])
        product.category_id = int(request.form['category_id'])
        product.featured = 'featured' in request.form

        # Handle file upload
        if 'image_file' in request.files:
            file = request.files['image_file']
            if file.filename != '':
                new_image_url = save_uploaded_file(file)
                if new_image_url:
                    # Delete old image file if it exists
                    if product.image_url and product.image_url.startswith('uploads/'):
                        old_file_path = os.path.join(app.static_folder, product.image_url)
                        if os.path.exists(old_file_path):
                            try:
                                os.remove(old_file_path)
                            except:
                                pass  # Ignore errors when deleting old file
                    product.image_url = new_image_url
                else:
                    flash('Invalid file type. Please upload PNG, JPG, JPEG, GIF, or WebP files.')
                    categories = Category.query.all()
                    return render_template('admin/edit_product.html', product=product, categories=categories)

        db.session.commit()

        # Clear cache when product is updated
        cache_store.clear()

        flash('Product updated successfully')
        return redirect(url_for('admin_products'))

    categories = Category.query.all()
    return render_template('admin/edit_product.html', product=product, categories=categories)

@app.route('/admin/products/delete/<int:product_id>', methods=['POST'])
@login_required
def admin_delete_product(product_id):
    if not current_user.is_admin:
        flash('Access denied')
        return redirect(url_for('home'))

    product = Product.query.get_or_404(product_id)
    db.session.delete(product)
    db.session.commit()
    flash('Product deleted successfully')
    return redirect(url_for('admin_products'))

# User Profile Routes
@app.route('/profile')
@login_required
def user_profile():
    profile = UserProfile.query.filter_by(user_id=current_user.id).first()
    if not profile:
        profile = UserProfile(user_id=current_user.id)
        db.session.add(profile)
        db.session.commit()

    # Get user's orders
    orders = Order.query.filter_by(user_id=current_user.id).order_by(Order.created_at.desc()).all()

    # Get user's reviews
    reviews = Review.query.filter_by(user_id=current_user.id).order_by(Review.created_at.desc()).all()

    return render_template('profile.html', profile=profile, orders=orders, reviews=reviews)

@app.route('/profile/edit', methods=['GET', 'POST'])
@login_required
def edit_profile():
    profile = UserProfile.query.filter_by(user_id=current_user.id).first()
    if not profile:
        profile = UserProfile(user_id=current_user.id)
        db.session.add(profile)
        db.session.commit()

    if request.method == 'POST':
        profile.first_name = request.form.get('first_name')
        profile.last_name = request.form.get('last_name')
        profile.phone = request.form.get('phone')
        profile.address = request.form.get('address')
        profile.city = request.form.get('city')
        profile.state = request.form.get('state')
        profile.zip_code = request.form.get('zip_code')
        profile.country = request.form.get('country')

        # Handle avatar upload
        if 'avatar_file' in request.files:
            file = request.files['avatar_file']
            if file.filename != '':
                avatar_url = save_uploaded_file(file)
                if avatar_url:
                    # Delete old avatar if it exists
                    if profile.avatar_url and profile.avatar_url.startswith('uploads/'):
                        old_file_path = os.path.join(app.static_folder, profile.avatar_url)
                        if os.path.exists(old_file_path):
                            try:
                                os.remove(old_file_path)
                            except:
                                pass
                    profile.avatar_url = avatar_url

        db.session.commit()
        flash('Profile updated successfully')
        return redirect(url_for('user_profile'))

    return render_template('edit_profile.html', profile=profile)

# Review Routes
@app.route('/product/<slug>/review', methods=['POST'])
@login_required
def add_review(slug):
    product = Product.query.filter_by(slug=slug).first_or_404()

    # Check if user already reviewed this product
    existing_review = Review.query.filter_by(user_id=current_user.id, product_id=product.id).first()

    # Get rating and validate it
    rating_value = request.form.get('rating')
    if not rating_value:
        flash('Please select a rating before submitting your review.')
        return redirect(url_for('product', slug=slug))
    
    try:
        rating = int(rating_value)
        if rating < 1 or rating > 5:
            flash('Rating must be between 1 and 5 stars.')
            return redirect(url_for('product', slug=slug))
    except ValueError:
        flash('Invalid rating value. Please select a valid rating.')
        return redirect(url_for('product', slug=slug))
    
    comment = request.form.get('comment', '').strip()

    if existing_review:
        # Update existing review
        existing_review.rating = rating
        existing_review.comment = comment
        existing_review.updated_at = datetime.utcnow()
        flash('Your review has been updated')
        print(f"Updated review: User {current_user.id}, Product {product.id}, Rating {rating}")
    else:
        # Create new review
        review = Review(
            user_id=current_user.id,
            product_id=product.id,
            rating=rating,
            comment=comment
        )
        db.session.add(review)
        flash('Thank you for your review!')
        print(f"Created new review: User {current_user.id}, Product {product.id}, Rating {rating}")

    db.session.commit()
    
    # Clear cache to ensure the updated review appears immediately
    cache_store.clear()
    
    return redirect(url_for('product', slug=slug))

@app.route('/review/<int:review_id>/delete', methods=['POST'])
@login_required
def delete_review(review_id):
    review = Review.query.get_or_404(review_id)

    if review.user_id != current_user.id:
        flash('You can only delete your own reviews')
        return redirect(url_for('user_profile'))

    product_slug = review.product.slug
    db.session.delete(review)
    db.session.commit()
    flash('Review deleted successfully')

    return redirect(url_for('product', slug=product_slug))

# Order Routes
@app.route('/checkout', methods=['GET', 'POST'])
@login_required
def checkout():
    cart_items = CartItem.query.filter_by(user_id=current_user.id).all()

    if not cart_items:
        flash('Your cart is empty')
        return redirect(url_for('cart'))

    if request.method == 'POST':
        print(f"DEBUG: Checkout form submitted by user {current_user.id}")
        print(f"DEBUG: Form data: {dict(request.form)}")
        
        # Check if cart is still not empty
        if not cart_items:
            flash('Your cart is empty. Please add items before checkout.', 'error')
            return redirect(url_for('cart'))
        
        # Update user profile with form data
        profile = UserProfile.query.filter_by(user_id=current_user.id).first()
        if not profile:
            profile = UserProfile(user_id=current_user.id)
            db.session.add(profile)
        
        # Enforce phone number
        phone = request.form.get('phone', '').strip()
        if not phone:
            flash('Phone number is required to place an order.', 'error')
            return redirect(url_for('checkout'))

        profile.first_name = request.form.get('first_name')
        profile.last_name = request.form.get('last_name')
        profile.phone = phone
        # Use combined shipping address if available, otherwise use individual fields
        combined_address = request.form.get('combined_shipping_address')
        if combined_address:
            profile.address = combined_address
        else:
            profile.address = request.form.get('shipping_address')
        
        profile.city = request.form.get('city')
        profile.state = request.form.get('state')
        profile.zip_code = request.form.get('zip_code')
        profile.country = request.form.get('country')
        
        # Create order
        total = sum(item.product.price * item.quantity for item in cart_items)

        # Get payment method
        payment_method = request.form.get('payment_method')
        
        # Set order status based on payment method
        if payment_method == 'pay_after_shipping':
            order_status = 'pending'
        else:
            order_status = 'confirmed'
        
        # Use combined shipping address for order if available
        shipping_address = request.form.get('combined_shipping_address') or request.form.get('shipping_address')
        
        order = Order(
            user_id=current_user.id,
            total_amount=total,
            shipping_address=shipping_address,
            status=order_status,
            payment_method=payment_method
        )
        db.session.add(order)
        db.session.flush()  # Get order ID

        # Create order items
        for cart_item in cart_items:
            order_item = OrderItem(
                order_id=order.id,
                product_id=cart_item.product_id,
                quantity=cart_item.quantity,
                price=cart_item.product.price
            )
            db.session.add(order_item)

        # Clear cart
        for cart_item in cart_items:
            db.session.delete(cart_item)

        db.session.commit()
        
        print(f"DEBUG: Order {order.id} created successfully for user {current_user.id}")
        
        # Send order confirmation email
        try:
            send_order_confirmation_email(current_user, order)
            print(f"Order confirmation email sent to {current_user.email}")
        except Exception as e:
            print(f"Failed to send order confirmation email: {e}")
        
        flash('🎉 Order placed successfully! Check your email for confirmation details.', 'success')
        return redirect(url_for('order_confirmation', order_id=order.id))

    total = sum(item.product.price * item.quantity for item in cart_items)
    return render_template('checkout.html', cart_items=cart_items, total=total)

@app.route('/order/<int:order_id>')
@login_required
def order_confirmation(order_id):
    order = Order.query.get_or_404(order_id)

    if order.user_id != current_user.id:
        flash('Access denied')
        return redirect(url_for('user_profile'))

    return render_template('order_confirmation.html', order=order)

@app.route('/test-checkout', methods=['POST'])
@login_required
def test_checkout():
    """Test route to debug checkout form submission"""
    print(f"DEBUG: Test checkout form submitted")
    print(f"DEBUG: Form data: {dict(request.form)}")
    print(f"DEBUG: User: {current_user.username}")
    return jsonify({'success': True, 'message': 'Form received'})

@app.route('/order/<int:order_id>/cancel', methods=['POST'])
@login_required
def cancel_order(order_id):
    order = Order.query.get_or_404(order_id)

    if order.user_id != current_user.id:
        flash('Access denied')
        return redirect(url_for('user_profile'))

    if order.status in ['pending', 'confirmed']:
        order.status = 'cancelled'
        order.updated_at = datetime.utcnow()
        db.session.commit()
        flash('Order cancelled successfully')
    else:
        flash('This order cannot be cancelled')

    return redirect(url_for('user_profile'))

# Debug route to check product images
@app.route('/debug/products')
def debug_products():
    if not (current_user.is_authenticated and current_user.is_admin):
        return "Access denied", 403

    products = Product.query.all()
    debug_info = []
    for product in products:
        debug_info.append({
            'id': product.id,
            'name': product.name,
            'image_url': product.image_url,
            'processed_url': image_url_filter(product.image_url)
        })

    return f"<pre>{json.dumps(debug_info, indent=2)}</pre>"


def init_db():
    try:
        db.create_all()

        # Create sample data if tables are empty
        if Category.query.count() == 0:
            categories = [
                Category(name='Fashion', slug='fashion', description='Trendy clothing and accessories'),
                Category(name='Beauty', slug='beauty', description='Skincare and cosmetics'),
                Category(name='Home', slug='home', description='Home decor and essentials'),
                Category(name='Tech', slug='tech', description='Latest gadgets and electronics'),
                Category(name='Pharmacy', slug='pharmacy', description='Health and wellness products')
            ]
            for cat in categories:
                db.session.add(cat)

            db.session.commit()

            # Add sample products
            products = [
                Product(name='Elegant Silk Dress', slug='elegant-silk-dress', description='Beautiful silk dress perfect for any occasion', price=129.99, category_id=1, featured=True, stock=10, image_url='/static/images/dress.jpg'),
                Product(name='Premium Face Serum', slug='premium-face-serum', description='Anti-aging serum with natural ingredients', price=79.99, category_id=2, featured=True, stock=15, image_url='/static/images/serum.jpg'),
                Product(name='Modern Table Lamp', slug='modern-table-lamp', description='Stylish lamp for your living space', price=89.99, category_id=3, featured=True, stock=8, image_url='/static/images/lamp.jpg'),
                Product(name='Wireless Headphones', slug='wireless-headphones', description='High-quality sound with noise cancellation', price=199.99, category_id=4, featured=True, stock=12, image_url='/static/images/headphones.jpg'),
            ]
            for product in products:
                db.session.add(product)

            db.session.commit()
    except Exception as e:
        db.session.rollback()
        print(f"Error initializing database: {e}")
        raise

# Create tables and initialize database
with app.app_context():
    try:
        db.create_all()
        init_db()
    except Exception as e:
        print(f"Error initializing database: {e}")
        raise

if __name__ == '__main__':
    app.run(debug=True)
