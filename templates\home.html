{% extends "base.html" %}

{% block content %}
<!-- Hero Section -->
<section class="relative bg-gradient-to-br from-indigo-900 via-purple-900 to-pink-600 text-white overflow-hidden">
    <div class="absolute inset-0 bg-black opacity-20"></div>
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24 lg:py-32">
        <div class="text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6 animate-fade-in-up">
                Shop Smart. <span class="text-pink-300">Live Beautifully.</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-indigo-100 animate-fade-in-up animation-delay-200">
                Discover curated products that elevate your lifestyle
            </p>
            <div class="space-x-4 animate-fade-in-up animation-delay-400">
                <a href="#featured" class="bg-white text-indigo-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300 inline-block">
                    Shop Now
                </a>
                <a href="#categories" class="border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-indigo-900 transition-colors duration-300 inline-block">
                    Explore Categories
                </a>
            </div>
        </div>
    </div>
    <div class="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1440 120" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0 120L60 105C120 90 240 60 360 45C480 30 600 30 720 37.5C840 45 960 60 1080 67.5C1200 75 1320 75 1380 75L1440 75V120H1380C1320 120 1200 120 1080 120C960 120 840 120 720 120C600 120 480 120 360 120C240 120 120 120 60 120H0Z" fill="rgb(249 250 251)"/>
        </svg>
    </div>
</section>

<!-- Categories Section -->
<section id="categories" class="py-16 bg-gray-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Shop by Category</h2>
            <p class="text-xl text-gray-600">Find exactly what you're looking for</p>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
            {% for category in categories %}
            <a href="{{ url_for('category', slug=category.slug) }}" class="group animate-fade-in-up animation-delay-{{ loop.index0 * 200 }}">
                <div class="category-card bg-white rounded-xl p-6 text-center shadow-md hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 card-shadow">
                    <div class="category-icon w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-indigo-100 to-pink-100 rounded-full flex items-center justify-center group-hover:from-indigo-200 group-hover:to-pink-200 transition-colors duration-300">
                        {% if category.slug == 'fashion' %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4 4 4 0 004-4V5z"></path>
                            </svg>
                        {% elif category.slug == 'beauty' %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        {% elif category.slug == 'home' %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                        {% elif category.slug == 'tech' %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 3v2m6-2v2M9 19v2m6-2v2M5 9H3m2 6H3m18-6h-2m2 6h-2M7 19h10a2 2 0 002-2V7a2 2 0 00-2-2H7a2 2 0 00-2 2v10a2 2 0 002 2zM9 9h6v6H9V9z"></path>
                            </svg>
                        {% elif category.slug == 'pharmacy' %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        {% else %}
                            <svg class="w-8 h-8 text-indigo-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                            </svg>
                        {% endif %}
                    </div>
                    <h3 class="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">{{ category.name }}</h3>
                </div>
            </a>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Featured Products -->
<section id="featured" class="py-16 bg-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Featured Products</h2>
            <p class="text-xl text-gray-600">Handpicked items just for you</p>
        </div>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
            {% for product in featured_products %}
            <div class="group product-card animate-fade-in-up animation-delay-{{ (loop.index0 * 100) + 400 }}">
                <div class="relative overflow-hidden rounded-xl bg-gray-100 aspect-square mb-4 card-shadow">
                    {% if product.image_url %}
                        <img src="{{ product.image_url | image_url }}"
                             alt="{{ product.name }}"
                             class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500 lazy-load"
                             loading="lazy"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
                             onload="this.classList.add('loaded')">
                    {% endif %}
                    <div class="w-full h-full bg-gradient-to-br from-indigo-100 to-pink-100 flex items-center justify-center {% if product.image_url %}hidden{% endif %}" style="{% if product.image_url %}display: none;{% endif %}">
                        <svg class="w-16 h-16 text-indigo-400 icon-float" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                        </svg>
                    </div>
                    <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300"></div>

                    <!-- Add to Cart Button -->
                    <button class="product-icon absolute top-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg icon-glow"
                            onclick="addToCart({{ product.id }}, 1)">
                        <svg class="w-5 h-5 text-gray-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
                        </svg>
                    </button>

                    <!-- Wishlist Button -->
                    <button class="product-icon absolute top-4 left-4 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg"
                            onclick="toggleWishlist({{ product.id }})">
                        <svg class="w-5 h-5 text-gray-600 heart-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </button>

                    <!-- Quick View Button -->
                    <button class="product-icon absolute bottom-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center shadow-lg"
                            onclick="quickView({{ product.id }})">
                        <svg class="w-5 h-5 text-gray-600 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                        </svg>
                    </button>
                </div>
                <div class="text-center">
                    <h3 class="font-semibold text-gray-900 mb-2 group-hover:text-indigo-600 transition-colors duration-300">
                        <a href="{{ url_for('product', slug=product.slug) }}" class="hover:underline">{{ product.name }}</a>
                    </h3>
                    <p class="text-gray-600 text-sm mb-2 flex items-center justify-center">
                        <svg class="w-4 h-4 mr-1 icon-hover" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
                        </svg>
                        {{ product.category.name }}
                    </p>
                    <div class="flex items-center justify-center mb-2">
                        {% for i in range(5) %}
                            <svg class="w-4 h-4 text-yellow-400 star-icon" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        {% endfor %}
                        <span class="ml-2 text-sm text-gray-500">(4.5)</span>
                    </div>
                    <p class="text-2xl font-bold text-indigo-900 animate-bounce-in">${{ "%.2f"|format(product.price) }}</p>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
</section>

<!-- Newsletter Section -->
<section class="py-16 bg-gradient-to-r from-indigo-900 to-pink-600">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
        <h2 class="text-3xl md:text-4xl font-bold text-white mb-4">Stay in the Loop</h2>
        <p class="text-xl text-indigo-100 mb-8">Get the latest updates on new arrivals and exclusive offers</p>
        <form class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto">
            <input type="email" placeholder="Enter your email" 
                   class="flex-1 px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-white focus:outline-none">
            <button type="submit" 
                    class="bg-white text-indigo-900 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors duration-300">
                Subscribe
            </button>
        </form>
    </div>
</section>
{% endblock %}
