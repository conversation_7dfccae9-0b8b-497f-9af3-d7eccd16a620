{% extends "admin/base.html" %}

{% block title %}Add Category - Admin Dashboard{% endblock %}
{% block page_title %}Add New Category{% endblock %}

{% block content %}
<div class="max-w-2xl">
    <div class="admin-card bg-white rounded-xl shadow-lg p-6">
        <div class="mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-2">Category Information</h3>
            <p class="text-gray-600">Create a new category to organize your products.</p>
        </div>

        <form method="POST" class="space-y-6">
            <div>
                <label for="name" class="block text-sm font-medium text-gray-700 mb-2">
                    Category Name *
                </label>
                <input type="text" 
                       id="name" 
                       name="name" 
                       required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                       placeholder="Enter category name"
                       onkeyup="generateSlug()">
            </div>

            <div>
                <label for="slug" class="block text-sm font-medium text-gray-700 mb-2">
                    URL Slug *
                </label>
                <input type="text" 
                       id="slug" 
                       name="slug" 
                       required 
                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                       placeholder="category-url-slug">
                <p class="text-sm text-gray-500 mt-1">This will be used in the URL. Use lowercase letters, numbers, and hyphens only.</p>
            </div>

            <div>
                <label for="description" class="block text-sm font-medium text-gray-700 mb-2">
                    Description
                </label>
                <textarea id="description" 
                          name="description" 
                          rows="4" 
                          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                          placeholder="Enter category description (optional)"></textarea>
            </div>

            <div class="flex items-center justify-between pt-6 border-t border-gray-200">
                <a href="{{ url_for('admin_categories') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Categories
                </a>
                <button type="submit" class="inline-flex items-center px-6 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
                    <i class="fas fa-save mr-2"></i>
                    Create Category
                </button>
            </div>
        </form>
    </div>
</div>

<script>
function generateSlug() {
    const name = document.getElementById('name').value;
    const slug = name.toLowerCase()
                     .replace(/[^a-z0-9\s-]/g, '')
                     .replace(/\s+/g, '-')
                     .replace(/-+/g, '-')
                     .trim('-');
    document.getElementById('slug').value = slug;
}
</script>
{% endblock %}
