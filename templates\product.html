{% extends "base.html" %}

{% block title %}{{ product.name }} - Amazora{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ url_for('home') }}" class="text-gray-700 hover:text-indigo-600">Home</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ url_for('category', slug=product.category.slug) }}" class="ml-1 text-gray-700 hover:text-indigo-600 md:ml-2">{{ product.category.name }}</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">{{ product.name }}</span>
                </div>
            </li>
        </ol>
    </nav>

    <!-- Product Details -->
    <div class="lg:grid lg:grid-cols-2 lg:gap-x-8 lg:items-start">
        <!-- Image -->
        <div class="flex flex-col-reverse">
            <div class="aspect-w-1 aspect-h-1 w-full">
                <img src="{{ product.image_url | image_url or '/placeholder.svg?height=600&width=600&query=' + product.name }}"
                     alt="{{ product.name }}"
                     class="w-full h-full object-center object-cover sm:rounded-lg">
            </div>
        </div>

        <!-- Product info -->
        <div class="mt-10 px-4 sm:px-0 sm:mt-16 lg:mt-0">
            <h1 class="text-3xl font-bold tracking-tight text-gray-900">{{ product.name }}</h1>
            
            <!-- Rating Display -->
            <div class="mt-3 flex items-center space-x-4">
                <div class="flex items-center">
                    {% for i in range(1, 6) %}
                        {% if i <= avg_rating %}
                            <i class="fas fa-star text-yellow-400"></i>
                        {% else %}
                            <i class="far fa-star text-gray-300"></i>
                        {% endif %}
                    {% endfor %}
                    <span class="ml-2 text-sm text-gray-600">{{ "%.1f"|format(avg_rating) }} ({{ reviews|length }} reviews)</span>
                </div>
            </div>
            
            <div class="mt-3">
                <h2 class="sr-only">Product information</h2>
                <p class="text-3xl tracking-tight text-indigo-900">${{ "%.2f"|format(product.price) }}</p>
            </div>

            <div class="mt-6">
                <h3 class="sr-only">Description</h3>
                <div class="text-base text-gray-700 space-y-6">
                    <p>{{ product.description or "This is a premium product carefully selected for our customers." }}</p>
                </div>
            </div>

            <div class="mt-6">
                <div class="flex items-center">
                    <span class="text-sm text-gray-500">Stock: </span>
                    <span class="ml-2 text-sm font-medium text-gray-900">{{ product.stock }} available</span>
                </div>
            </div>

            <form class="mt-6">
                <!-- Quantity -->
                <div class="flex items-center space-x-4 mb-6">
                    <label for="quantity" class="text-sm font-medium text-gray-700">Quantity:</label>
                    <select id="quantity" name="quantity" class="rounded-md border border-gray-300 text-base font-medium text-gray-700 text-left shadow-sm focus:outline-none focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500">
                        {% for i in range(1, max_quantity) %}
                        <option value="{{ i }}">{{ i }}</option>
                        {% endfor %}
                    </select>
                </div>

                <div class="flex space-x-4">
                    <button type="button" 
                            id="add-to-cart-btn"
                            data-product-id="{{ product.id }}"
                            class="flex-1 bg-gradient-to-r from-indigo-900 to-pink-600 text-white py-3 px-8 rounded-lg font-semibold hover:opacity-90 transition-opacity duration-300 flex items-center justify-center">
                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
                        </svg>
                        Add to Cart
                    </button>
                    <button type="button" class="p-3 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors duration-300">
                        <svg class="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="mt-16">
        <div class="border-t border-gray-200 pt-10">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Customer Reviews</h2>
            
            <!-- Review Summary -->
            <div class="bg-gray-50 rounded-lg p-6 mb-8">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-4">
                        <div class="text-center">
                            <div class="text-3xl font-bold text-gray-900">{{ "%.1f"|format(avg_rating) }}</div>
                            <div class="flex items-center justify-center mt-1">
                                {% for i in range(1, 6) %}
                                    {% if i <= avg_rating %}
                                        <i class="fas fa-star text-yellow-400 text-sm"></i>
                                    {% else %}
                                        <i class="far fa-star text-gray-300 text-sm"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <div class="text-sm text-gray-600 mt-1">out of 5</div>
                        </div>
                        <div class="text-sm text-gray-600">
                            <p>Based on {{ reviews|length }} reviews</p>
                        </div>
                    </div>
                    
                    {% if current_user.is_authenticated %}
                        <button onclick="toggleReviewForm()" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                            <i class="fas fa-star mr-2"></i>
                            {% if user_review %}Edit Review{% else %}Write a Review{% endif %}
                        </button>
                    {% else %}
                        <a href="{{ url_for('login') }}" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                            <i class="fas fa-sign-in-alt mr-2"></i>
                            Login to Review
                        </a>
                    {% endif %}
                </div>
            </div>

            <!-- Review Form (Hidden by default) -->
            {% if current_user.is_authenticated %}
            <div id="reviewForm" class="bg-white border border-gray-200 rounded-lg p-6 mb-8" style="display: none;">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    {% if user_review %}Edit Your Review{% else %}Write Your Review{% endif %}
                </h3>
                {% if user_review %}
                <div class="mb-4 p-3 bg-blue-50 rounded-lg">
                    <p class="text-sm text-blue-800">
                        <i class="fas fa-info-circle mr-1"></i>
                        You previously rated this product {{ user_review.rating }} star{{ 's' if user_review.rating != 1 else '' }}.
                    </p>
                </div>
                {% endif %}
                <form method="POST" action="{{ url_for('add_review', slug=product.slug) }}" id="reviewFormElement">
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Rating <span class="text-red-500">*</span></label>
                        <div class="flex items-center space-x-2" id="rating-container">
                            {% for i in range(1, 6) %}
                            <input type="radio" id="rating{{ i }}" name="rating" value="{{ i }}" 
                                   {% if user_review and user_review.rating == i %}checked{% endif %}
                                   class="sr-only" required>
                            <label for="rating{{ i }}" class="cursor-pointer text-2xl text-gray-300 hover:text-yellow-400 transition-colors duration-200 rating-star" data-rating="{{ i }}">
                                <i class="fas fa-star"></i>
                            </label>
                            {% endfor %}
                        </div>
                        <p class="text-sm text-gray-500 mt-1">Click on a star to rate this product (1-5 stars)</p>
                        <p id="rating-error" class="text-sm text-red-600 mt-1" style="display: none;">Please select a rating before submitting.</p>
                        <p id="rating-selected" class="text-sm text-green-600 mt-1" style="display: none;">Rating selected!</p>
                    </div>
                    <div class="mb-4">
                        <label for="comment" class="block text-sm font-medium text-gray-700 mb-2">Comment</label>
                        <textarea id="comment" name="comment" rows="4" 
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                  placeholder="Share your experience with this product...">{{ user_review.comment if user_review else '' }}</textarea>
                    </div>
                    <div class="flex items-center justify-between">
                        <button type="button" onclick="toggleReviewForm()" class="text-gray-600 hover:text-gray-800">
                            Cancel
                        </button>
                        <button type="submit" class="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200">
                            <i class="fas fa-paper-plane mr-2"></i>
                            {% if user_review %}Update Review{% else %}Submit Review{% endif %}
                        </button>
                    </div>
                </form>
            </div>
            {% endif %}

            <!-- Reviews List -->
            {% if reviews %}
                <div class="space-y-6">
                    {% for review in reviews %}
                    <div class="bg-white border border-gray-200 rounded-lg p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold text-sm">
                                        {{ review.user.username[:2].upper() }}
                                    </span>
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">{{ review.user.username }}</h4>
                                    <div class="flex items-center space-x-1">
                                        {% for i in range(1, 6) %}
                                            {% if i <= review.rating %}
                                                <i class="fas fa-star text-yellow-400 text-sm"></i>
                                            {% else %}
                                                <i class="far fa-star text-gray-300 text-sm"></i>
                                            {% endif %}
                                        {% endfor %}
                                        <span class="text-sm text-gray-600 ml-2">{{ review.created_at.strftime('%B %d, %Y') }}</span>
                                    </div>
                                </div>
                            </div>
                            {% if current_user.is_authenticated and review.user_id == current_user.id %}
                                <form method="POST" action="{{ url_for('delete_review', review_id=review.id) }}" class="inline" onsubmit="return confirm('Are you sure you want to delete this review?')">
                                    <button type="submit" class="text-red-600 hover:text-red-800 text-sm">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            {% endif %}
                        </div>
                        {% if review.comment %}
                            <p class="text-gray-700">{{ review.comment }}</p>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-star text-gray-400 text-4xl mb-4"></i>
                    <h3 class="text-lg font-medium text-gray-900 mb-2">No reviews yet</h3>
                    <p class="text-gray-500">Be the first to review this product!</p>
                </div>
            {% endif %}
        </div>
    </div>

    <!-- Related Products -->
    {% if related_products %}
    <div class="mt-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8">You might also like</h2>
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
            {% for related_product in related_products %}
            <div class="group">
                <div class="relative overflow-hidden rounded-lg bg-gray-100 aspect-square mb-4">
                    <img src="{{ related_product.image_url | image_url or '/placeholder.svg?height=300&width=300&query=' + related_product.name }}"
                         alt="{{ related_product.name }}"
                         class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
                    <button class="absolute top-4 right-4 w-10 h-10 bg-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                            data-product-id="{{ related_product.id }}">
                        <svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9m-9 0V19a2 2 0 002 2h7a2 2 0 002-2v-4"></path>
                        </svg>
                    </button>
                </div>
                <h3 class="font-semibold text-gray-900 group-hover:text-indigo-600 transition-colors duration-300">
                    <a href="{{ url_for('product', slug=related_product.slug) }}">{{ related_product.name }}</a>
                </h3>
                <p class="text-lg font-bold text-indigo-900 mt-2">${{ "%.2f"|format(related_product.price) }}</p>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<script>
function toggleReviewForm() {
    const form = document.getElementById('reviewForm');
    if (form.style.display === 'none') {
        form.style.display = 'block';
    } else {
        form.style.display = 'none';
    }
}

// Star rating interaction
document.addEventListener('DOMContentLoaded', function() {
    const ratingContainer = document.getElementById('rating-container');
    if (!ratingContainer) return;
    
    const ratingInputs = document.querySelectorAll('input[name="rating"]');
    const ratingStars = document.querySelectorAll('.rating-star');
    
    // Initialize stars based on existing rating
    function initializeStars() {
        const checkedRating = document.querySelector('input[name="rating"]:checked');
        if (checkedRating) {
            const rating = parseInt(checkedRating.value);
            updateStarDisplay(rating);
        }
    }
    
    // Update star display
    function updateStarDisplay(rating) {
        ratingStars.forEach((star, index) => {
            const starIcon = star.querySelector('i');
            if (index < rating) {
                starIcon.className = 'fas fa-star text-yellow-400';
            } else {
                starIcon.className = 'fas fa-star text-gray-300';
            }
        });
        
        // Update the rating error message
        const ratingError = document.getElementById('rating-error');
        if (ratingError) {
            if (rating > 0) {
                ratingError.style.display = 'none';
            }
        }
    }
    
    // Handle star hover
    ratingStars.forEach((star, index) => {
        star.addEventListener('mouseenter', function() {
            updateStarDisplay(index + 1);
        });
        
        star.addEventListener('click', function() {
            const rating = index + 1;
            // Uncheck all radio buttons first
            ratingInputs.forEach(input => input.checked = false);
            // Check the clicked rating
            document.getElementById(`rating${rating}`).checked = true;
            updateStarDisplay(rating);
            
            // Show success message
            const ratingSelected = document.getElementById('rating-selected');
            const ratingError = document.getElementById('rating-error');
            if (ratingSelected) {
                ratingSelected.style.display = 'block';
                ratingSelected.textContent = `Rating selected: ${rating} star${rating !== 1 ? 's' : ''}!`;
            }
            if (ratingError) {
                ratingError.style.display = 'none';
            }
            
            // Add visual feedback
            star.style.transform = 'scale(1.1)';
            setTimeout(() => {
                star.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // Handle mouse leave
    ratingContainer.addEventListener('mouseleave', function() {
        const checkedRating = document.querySelector('input[name="rating"]:checked');
        if (checkedRating) {
            updateStarDisplay(parseInt(checkedRating.value));
        } else {
            updateStarDisplay(0);
        }
    });
    
    // Initialize on page load
    initializeStars();
    
    // Add form validation
    const reviewForm = document.getElementById('reviewFormElement');
    if (reviewForm) {
        reviewForm.addEventListener('submit', function(e) {
            const selectedRating = document.querySelector('input[name="rating"]:checked');
            const ratingError = document.getElementById('rating-error');
            
            if (!selectedRating) {
                e.preventDefault();
                ratingError.style.display = 'block';
                return false;
            } else {
                ratingError.style.display = 'none';
            }
        });
    }
    
    // Add to cart button event listener
    const addToCartBtn = document.getElementById('add-to-cart-btn');
    if (addToCartBtn) {
        addToCartBtn.addEventListener('click', function() {
            const productId = this.getAttribute('data-product-id');
            const quantity = document.getElementById('quantity').value;
            addToCart(productId, quantity);
        });
    }
    
    // Related products add to cart buttons
    document.querySelectorAll('[data-product-id]').forEach(button => {
        if (!button.id) { // Skip the main add to cart button
            button.addEventListener('click', function() {
                const productId = this.getAttribute('data-product-id');
                addToCart(productId, 1);
            });
        }
    });
});

function addToCart(productId, quantity) {
    fetch('/add_to_cart', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            product_id: productId,
            quantity: parseInt(quantity)
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            alert('Product added to cart successfully!');
            // Trigger cart count update
            window.dispatchEvent(new Event('cartUpdated'));
        } else {
            alert('Error adding product to cart');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error adding product to cart');
    });
}
</script>
{% endblock %}
