/* Custom CSS for Amazora */
.font-poppins {
    font-family: 'Poppins', sans-serif;
}

/* Navigation Styles */
.nav-link {
    color: #6b7280;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.3s ease;
    text-decoration: none;
}

.nav-link:hover {
    color: #e91e63;
}

.mobile-nav-link {
    color: #6b7280;
    display: block;
    padding: 0.5rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 1rem;
    font-weight: 500;
    transition: color 0.3s ease;
    text-decoration: none;
}

.mobile-nav-link:hover {
    color: #e91e63;
}

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes wiggle {
    0%, 7% {
        transform: rotateZ(0);
    }
    15% {
        transform: rotateZ(-15deg);
    }
    20% {
        transform: rotateZ(10deg);
    }
    25% {
        transform: rotateZ(-10deg);
    }
    30% {
        transform: rotateZ(6deg);
    }
    35% {
        transform: rotateZ(-4deg);
    }
    40%, 100% {
        transform: rotateZ(0);
    }
}

@keyframes heartbeat {
    0% {
        transform: scale(1);
    }
    14% {
        transform: scale(1.3);
    }
    28% {
        transform: scale(1);
    }
    42% {
        transform: scale(1.3);
    }
    70% {
        transform: scale(1);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideInLeft {
    from {
        opacity: 0;
        transform: translateX(-100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes zoomIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes rubberBand {
    from {
        transform: scale3d(1, 1, 1);
    }
    30% {
        transform: scale3d(1.25, 0.75, 1);
    }
    40% {
        transform: scale3d(0.75, 1.25, 1);
    }
    50% {
        transform: scale3d(1.15, 0.85, 1);
    }
    65% {
        transform: scale3d(0.95, 1.05, 1);
    }
    75% {
        transform: scale3d(1.05, 0.95, 1);
    }
    to {
        transform: scale3d(1, 1, 1);
    }
}

.animate-fade-in-up {
    animation: fadeInUp 0.8s ease-out forwards;
}

.animate-bounce-in {
    animation: bounceIn 0.6s ease-out forwards;
}

.animate-wiggle {
    animation: wiggle 2s ease-in-out;
}

.animate-heartbeat {
    animation: heartbeat 1.5s ease-in-out infinite;
}

.animate-float {
    animation: float 3s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

.animate-slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
}

.animate-slide-in-left {
    animation: slideInLeft 0.5s ease-out forwards;
}

.animate-zoom-in {
    animation: zoomIn 0.3s ease-out forwards;
}

.animate-rubber-band {
    animation: rubberBand 1s ease-out;
}

.animation-delay-200 {
    animation-delay: 0.2s;
    opacity: 0;
}

.animation-delay-400 {
    animation-delay: 0.4s;
    opacity: 0;
}

.animation-delay-600 {
    animation-delay: 0.6s;
    opacity: 0;
}

.animation-delay-800 {
    animation-delay: 0.8s;
    opacity: 0;
}

/* Product Card Hover Effects */
.product-card {
    transition: all 0.3s ease;
}

.product-card:hover {
    transform: translateY(-5px);
}

/* Flash Messages */
.flash-message {
    animation: slideInDown 0.5s ease-out;
}

@keyframes slideInDown {
    from {
        transform: translateY(-100%);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

/* Cart Drawer */
.cart-drawer-open #cart-content {
    transform: translateX(0);
}

/* Button Hover Effects */
.btn-primary {
    background: linear-gradient(to right, #1e1b4b, #e91e63);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: opacity 0.3s ease;
    border: none;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-primary:hover {
    opacity: 0.9;
}

.btn-secondary {
    border: 2px solid #1e1b4b;
    color: #1e1b4b;
    padding: 0.75rem 1.5rem;
    border-radius: 0.5rem;
    font-weight: 600;
    transition: all 0.3s ease;
    background: transparent;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    background: #1e1b4b;
    color: white;
}

/* Form Styles */
.form-input {
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    font-size: 0.875rem;
}

.form-input:focus {
    outline: none;
    border-color: #6366f1;
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Loading States */
.loading {
    opacity: 0.5;
    pointer-events: none;
}

/* Responsive Design */
@media (max-width: 640px) {
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1.25rem;
    }
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(to bottom, #1B2141, #E91E63);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(to bottom, #E91E63, #1B2141);
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus States */
.focus-visible:focus {
    outline: none;
    box-shadow: 0 0 0 2px #6366f1, 0 0 0 4px rgba(99, 102, 241, 0.2);
}

/* Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #1B2141, #E91E63);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Logo specific styles */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-blue-600 {
    --tw-gradient-from: #2563eb;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}

.to-indigo-900 {
    --tw-gradient-to: #1e1b4b;
}

.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

.text-transparent {
    color: transparent;
}

/* Logo animation */
.logo-container {
    position: relative;
    overflow: hidden;
}

.logo-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.logo-container:hover::before {
    left: 100%;
}

/* Enhanced logo effects */
.logo-glow {
    filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
    transition: filter 0.3s ease;
}

.logo-glow:hover {
    filter: drop-shadow(0 0 20px rgba(59, 130, 246, 0.5));
}

/* Large logo styles for auth pages */
.auth-logo {
    filter: drop-shadow(0 10px 25px rgba(59, 130, 246, 0.2));
    transition: all 0.3s ease;
}

.auth-logo:hover {
    transform: scale(1.05);
    filter: drop-shadow(0 15px 35px rgba(59, 130, 246, 0.3));
}

/* Header logo responsive sizing */
@media (max-width: 640px) {
    .header-logo {
        height: 2.5rem; /* h-10 */
    }
}

@media (min-width: 641px) {
    .header-logo {
        height: 3rem; /* h-12 */
    }
}

@media (min-width: 768px) {
    .header-logo {
        height: 3.5rem; /* h-14 */
    }
}

/* Performance optimizations */
.lazy-load {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.lazy-load.loaded {
    opacity: 1;
}

/* Faster animations for better performance */
.fast-fade {
    transition: opacity 0.2s ease;
}

.fast-scale {
    transition: transform 0.2s ease;
}

/* Optimize loading screen */
#loading-screen {
    will-change: opacity;
    backface-visibility: hidden;
}

/* Optimize logo animations */
.logo-glow {
    will-change: filter;
    backface-visibility: hidden;
}

/* Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Loading Screen Animations */
.animate-bounce-slow {
    animation: bounce-slow 2s infinite;
}

@keyframes bounce-slow {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

/* Login/Register Form Animations */
.form-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.input-focus {
    transition: all 0.3s ease;
}

.input-focus:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Gradient backgrounds for auth pages */
.auth-bg {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.auth-bg-register {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    min-height: 100vh;
}

/* Social button hover effects */
.social-btn {
    transition: all 0.3s ease;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* Loading dots animation */
@keyframes loading-dots {
    0%, 80%, 100% {
        transform: scale(0);
        opacity: 0.5;
    }
    40% {
        transform: scale(1);
        opacity: 1;
    }
}

.loading-dot {
    animation: loading-dots 1.4s infinite ease-in-out both;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dot:nth-child(3) { animation-delay: 0s; }

/* Card Shadows */
.card-shadow {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: box-shadow 0.3s ease;
}

.card-shadow:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Pulse Animation for Loading */
@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Additional Layout Fixes */
body {
    margin: 0;
    padding: 0;
    min-height: 100vh;
    background-color: #f9fafb;
}

/* Navigation fixes */
nav {
    background: white;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 50;
}

/* Container and spacing fixes */
.max-w-7xl {
    max-width: 80rem;
}

.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
}

/* Flexbox utilities */
.flex {
    display: flex;
}

.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-4 > * + * {
    margin-top: 1rem;
}

.space-y-6 > * + * {
    margin-top: 1.5rem;
}

.space-y-8 > * + * {
    margin-top: 2rem;
}

/* Text utilities */
.text-center {
    text-align: center;
}

.text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
}

.text-sm {
    font-size: 0.875rem;
    line-height: 1.25rem;
}

.font-bold {
    font-weight: 700;
}

.font-medium {
    font-weight: 500;
}

/* Color utilities */
.text-gray-900 {
    color: #111827;
}

.text-gray-600 {
    color: #6b7280;
}

.text-indigo-600 {
    color: #4f46e5;
}

.text-indigo-500 {
    color: #6366f1;
}

/* Width and height utilities */
.w-full {
    width: 100%;
}

.min-h-screen {
    min-height: 100vh;
}

.max-w-md {
    max-width: 28rem;
}

/* Border utilities */
.border {
    border-width: 1px;
}

.border-gray-300 {
    border-color: #d1d5db;
}

.border-transparent {
    border-color: transparent;
}

.rounded-md {
    border-radius: 0.375rem;
}

/* Background utilities */
.bg-gray-50 {
    background-color: #f9fafb;
}

.bg-white {
    background-color: white;
}

/* Padding and margin utilities */
.mt-1 {
    margin-top: 0.25rem;
}

.mt-2 {
    margin-top: 0.5rem;
}

.mt-6 {
    margin-top: 1.5rem;
}

.mt-8 {
    margin-top: 2rem;
}

.px-3 {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
}

.py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
}

/* Focus states */
.focus\:outline-none:focus {
    outline: none;
}

.focus\:ring-indigo-500:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.focus\:border-indigo-500:focus {
    border-color: #6366f1;
}

/* Hover states */
.hover\:text-indigo-500:hover {
    color: #6366f1;
}

.hover\:opacity-90:hover {
    opacity: 0.9;
}

/* Transition utilities */
.transition-colors {
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out;
}

.transition-opacity {
    transition: opacity 0.15s ease-in-out;
}

.duration-300 {
    transition-duration: 0.3s;
}

/* Additional utility classes for better layout */
.h-16 {
    height: 4rem;
}

.text-2xl {
    font-size: 1.5rem;
    line-height: 2rem;
}

.text-indigo-900 {
    color: #1e1b4b;
}

.text-pink-600 {
    color: #e91e63;
}

.flex-shrink-0 {
    flex-shrink: 0;
}

.hidden {
    display: none;
}

.md\:block {
    display: block;
}

.md\:hidden {
    display: none;
}

.ml-10 {
    margin-left: 2.5rem;
}

.items-baseline {
    align-items: baseline;
}

.relative {
    position: relative;
}

.absolute {
    position: absolute;
}

.fixed {
    position: fixed;
}

.inset-0 {
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
}

.top-0 {
    top: 0;
}

.right-0 {
    right: 0;
}

.z-50 {
    z-index: 50;
}

.w-6 {
    width: 1.5rem;
}

.h-6 {
    height: 1.5rem;
}

.p-2 {
    padding: 0.5rem;
}

.p-4 {
    padding: 1rem;
}

.-top-2 {
    top: -0.5rem;
}

.-right-2 {
    right: -0.5rem;
}

.bg-pink-600 {
    background-color: #e91e63;
}

.text-white {
    color: white;
}

.text-xs {
    font-size: 0.75rem;
    line-height: 1rem;
}

.rounded-full {
    border-radius: 9999px;
}

.h-5 {
    height: 1.25rem;
}

.w-5 {
    width: 1.25rem;
}

.shadow-lg {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.sticky {
    position: sticky;
}

/* Responsive utilities */
@media (min-width: 768px) {
    .md\:block {
        display: block;
    }

    .md\:hidden {
        display: none;
    }
}

@media (max-width: 767px) {
    .hidden.md\:hidden {
        display: none;
    }
}

/* Form specific improvements */
.appearance-none {
    appearance: none;
}

.block {
    display: block;
}

.relative {
    position: relative;
}

.placeholder-gray-500::placeholder {
    color: #6b7280;
}

.text-gray-900 {
    color: #111827;
}

.focus\:ring-2:focus {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
}

.focus\:ring-offset-2:focus {
    box-shadow: 0 0 0 2px white, 0 0 0 4px rgba(99, 102, 241, 0.1);
}

.group {
    /* Group utility for hover effects */
}

/* Gradient backgrounds */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.from-indigo-900 {
    --tw-gradient-from: #1e1b4b;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(30, 27, 75, 0));
}

.to-pink-600 {
    --tw-gradient-to: #e91e63;
}

/* Border utilities */
.border-t {
    border-top-width: 1px;
}

.border-b {
    border-bottom-width: 1px;
}

/* Overflow utilities */
.overflow-y-auto {
    overflow-y: auto;
}

/* Transform utilities */
.transform {
    transform: var(--tw-transform);
}

.translate-x-full {
    --tw-translate-x: 100%;
    transform: translateX(var(--tw-translate-x));
}

/* Shadow utilities */
.shadow-xl {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Spacing utilities */
.mb-4 {
    margin-bottom: 1rem;
}

.text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
}

.font-semibold {
    font-weight: 600;
}

.text-gray-400 {
    color: #9ca3af;
}

.text-gray-600 {
    color: #6b7280;
}

.hover\:text-gray-600:hover {
    color: #6b7280;
}

.flex-1 {
    flex: 1 1 0%;
}

/* Grid utilities */
.grid {
    display: grid;
}

.grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
}

.gap-4 {
    gap: 1rem;
}

.gap-6 {
    gap: 1.5rem;
}

.gap-8 {
    gap: 2rem;
}

/* Text size utilities */
.text-4xl {
    font-size: 2.25rem;
    line-height: 2.5rem;
}

.text-6xl {
    font-size: 3.75rem;
    line-height: 1;
}

.text-xl {
    font-size: 1.25rem;
    line-height: 1.75rem;
}

/* Color utilities */
.text-pink-300 {
    color: #f9a8d4;
}

.text-indigo-100 {
    color: #e0e7ff;
}

.text-indigo-600 {
    color: #4f46e5;
}

.text-gray-100 {
    color: #f3f4f6;
}

.bg-indigo-100 {
    background-color: #e0e7ff;
}

.bg-pink-100 {
    background-color: #fce7f3;
}

.bg-indigo-200 {
    background-color: #c7d2fe;
}

.bg-pink-200 {
    background-color: #fbcfe8;
}

.bg-pink-50 {
    background-color: #fdf2f8;
}

/* Spacing utilities */
.py-16 {
    padding-top: 4rem;
    padding-bottom: 4rem;
}

.py-24 {
    padding-top: 6rem;
    padding-bottom: 6rem;
}

.py-32 {
    padding-top: 8rem;
    padding-bottom: 8rem;
}

.px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
}

.mb-6 {
    margin-bottom: 1.5rem;
}

.mb-8 {
    margin-bottom: 2rem;
}

.mb-12 {
    margin-bottom: 3rem;
}

/* Border radius utilities */
.rounded-xl {
    border-radius: 0.75rem;
}

.rounded-lg {
    border-radius: 0.5rem;
}

/* Width and height utilities */
.w-8 {
    width: 2rem;
}

.h-8 {
    height: 2rem;
}

.w-10 {
    width: 2.5rem;
}

.h-10 {
    height: 2.5rem;
}

.w-16 {
    width: 4rem;
}

.h-16 {
    height: 4rem;
}

/* Display utilities */
.inline-block {
    display: inline-block;
}

/* Overflow utilities */
.overflow-hidden {
    overflow: hidden;
}

/* Position utilities */
.bottom-0 {
    bottom: 0;
}

.left-0 {
    left: 0;
}

.right-0 {
    right: 0;
}

.top-4 {
    top: 1rem;
}

.right-4 {
    right: 1rem;
}

/* Opacity utilities */
.opacity-20 {
    opacity: 0.2;
}

.opacity-0 {
    opacity: 0;
}

.opacity-100 {
    opacity: 1;
}

/* Background opacity utilities */
.bg-opacity-0 {
    background-color: rgba(0, 0, 0, 0);
}

.bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2);
}

.bg-opacity-50 {
    background-color: rgba(0, 0, 0, 0.5);
}

/* Aspect ratio utilities */
.aspect-square {
    aspect-ratio: 1 / 1;
}

/* Object fit utilities */
.object-cover {
    object-fit: cover;
}

/* Group hover utilities */
.group:hover .group-hover\:opacity-100 {
    opacity: 1;
}

.group:hover .group-hover\:bg-opacity-20 {
    background-color: rgba(0, 0, 0, 0.2);
}

.group:hover .group-hover\:scale-110 {
    transform: scale(1.1);
}

.group:hover .group-hover\:text-indigo-600 {
    color: #4f46e5;
}

.group:hover .group-hover\:from-indigo-200 {
    --tw-gradient-from: #c7d2fe;
}

.group:hover .group-hover\:to-pink-200 {
    --tw-gradient-to: #fbcfe8;
}

.group:hover .group-hover\:-translate-y-2 {
    transform: translateY(-0.5rem);
}

/* Responsive utilities */
@media (min-width: 640px) {
    .sm\:grid-cols-2 {
        grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    .sm\:px-6 {
        padding-left: 1.5rem;
        padding-right: 1.5rem;
    }

    .sm\:flex-row {
        flex-direction: row;
    }
}

@media (min-width: 768px) {
    .md\:grid-cols-3 {
        grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    .md\:text-4xl {
        font-size: 2.25rem;
        line-height: 2.5rem;
    }

    .md\:text-6xl {
        font-size: 3.75rem;
        line-height: 1;
    }

    .md\:text-2xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }
}

@media (min-width: 1024px) {
    .lg\:grid-cols-4 {
        grid-template-columns: repeat(4, minmax(0, 1fr));
    }

    .lg\:grid-cols-5 {
        grid-template-columns: repeat(5, minmax(0, 1fr));
    }

    .lg\:px-8 {
        padding-left: 2rem;
        padding-right: 2rem;
    }

    .lg\:py-32 {
        padding-top: 8rem;
        padding-bottom: 8rem;
    }
}

/* Flex utilities */
.flex-col {
    flex-direction: column;
}

.max-w-4xl {
    max-width: 56rem;
}

/* Additional gradient utilities */
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.from-indigo-900 {
    --tw-gradient-from: #1e1b4b;
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(30, 27, 75, 0));
}

.via-purple-900 {
    --tw-gradient-to: rgba(88, 28, 135, 0);
    --tw-gradient-stops: var(--tw-gradient-from), #581c87, var(--tw-gradient-to, rgba(88, 28, 135, 0));
}

.to-pink-600 {
    --tw-gradient-to: #e91e63;
}

/* Additional fixes for layout issues */
.space-x-4 > * + * {
    margin-left: 1rem;
}

.space-y-1 > * + * {
    margin-top: 0.25rem;
}

.border-2 {
    border-width: 2px;
}

.border-white {
    border-color: white;
}

.hover\:bg-gray-100:hover {
    background-color: #f3f4f6;
}

.hover\:bg-white:hover {
    background-color: white;
}

.hover\:text-indigo-900:hover {
    color: #1e1b4b;
}

.transition-transform {
    transition: transform 0.15s ease-in-out;
}

.duration-500 {
    transition-duration: 0.5s;
}

.duration-300 {
    transition-duration: 0.3s;
}

.scale-110 {
    transform: scale(1.1);
}

.-translate-y-2 {
    transform: translateY(-0.5rem);
}

.hover\:scale-110:hover {
    transform: scale(1.1);
}

.hover\:-translate-y-2:hover {
    transform: translateY(-0.5rem);
}

.transition-all {
    transition: all 0.15s ease-in-out;
}

/* Fix for missing image handling */
img {
    max-width: 100%;
    height: auto;
}

/* Ensure proper box sizing */
*, *::before, *::after {
    box-sizing: border-box;
}

/* Fix for gradient backgrounds */
.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

/* Additional responsive fixes */
@media (max-width: 639px) {
    .text-4xl {
        font-size: 2rem;
        line-height: 2.25rem;
    }

    .text-6xl {
        font-size: 2.5rem;
        line-height: 1.1;
    }

    .px-8 {
        padding-left: 1rem;
        padding-right: 1rem;
    }

    .py-24 {
        padding-top: 3rem;
        padding-bottom: 3rem;
    }
}

/* Ensure proper text rendering */
body {
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Fix for button styling */
button {
    cursor: pointer;
    border: none;
    background: none;
}

button:focus {
    outline: none;
}

/* Fix for link styling */
a {
    text-decoration: none;
    color: inherit;
}

/* Ensure proper flex behavior */
.items-center {
    align-items: center;
}

.justify-center {
    justify-content: center;
}

.justify-between {
    justify-content: space-between;
}

/* Fix for SVG sizing */
svg {
    display: block;
}

/* Additional margin utilities */
.mx-auto {
    margin-left: auto;
    margin-right: auto;
}

.my-4 {
    margin-top: 1rem;
    margin-bottom: 1rem;
}

/* Icon Animation Classes */
.icon-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.icon-hover:hover {
    transform: scale(1.1);
}

.icon-bounce:hover {
    animation: bounceIn 0.6s ease-out;
}

.icon-wiggle:hover {
    animation: wiggle 0.8s ease-in-out;
}

.icon-spin:hover {
    animation: spin 0.5s linear;
}

.icon-heartbeat:hover {
    animation: heartbeat 1s ease-in-out;
}

.icon-float {
    animation: float 3s ease-in-out infinite;
}

.icon-rubber-band:hover {
    animation: rubberBand 0.8s ease-out;
}

/* Cart Icon Specific Animations */
.cart-icon {
    position: relative;
    transition: all 0.3s ease;
}

.cart-icon:hover {
    transform: scale(1.1);
}

.cart-icon:hover svg {
    animation: wiggle 0.6s ease-in-out;
}

.cart-icon .cart-count {
    transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.cart-icon:hover .cart-count {
    transform: scale(1.2);
    animation: heartbeat 0.8s ease-in-out;
}

.cart-icon.cart-updated .cart-count {
    animation: bounceIn 0.6s ease-out;
}

/* Navigation Icon Animations */
.nav-icon {
    transition: all 0.3s ease;
    transform-origin: center;
}

.nav-icon:hover {
    transform: scale(1.05) rotate(5deg);
}

.nav-icon svg {
    transition: all 0.3s ease;
}

.nav-icon:hover svg {
    transform: scale(1.1);
}

/* Mobile Menu Icon Animation */
.mobile-menu-icon {
    transition: all 0.3s ease;
}

.mobile-menu-icon:hover {
    transform: scale(1.1);
}

.mobile-menu-icon svg {
    transition: all 0.3s ease;
}

.mobile-menu-icon:hover svg {
    animation: spin 0.3s ease-in-out;
}

.mobile-menu-icon.active svg {
    transform: rotate(90deg);
}

/* User Menu Icon Animation */
.user-menu-icon {
    transition: all 0.3s ease;
}

.user-menu-icon:hover {
    transform: scale(1.05);
}

.user-menu-icon svg {
    transition: transform 0.3s ease;
}

.user-menu-icon.active svg {
    transform: rotate(180deg);
}

/* Product Card Icon Animations */
.product-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: scale(0);
    opacity: 0;
}

.product-card:hover .product-icon {
    transform: scale(1);
    opacity: 1;
    animation: bounceIn 0.4s ease-out;
}

.product-icon:hover {
    transform: scale(1.1);
    background-color: #fdf2f8;
}

.product-icon svg {
    transition: all 0.3s ease;
}

.product-icon:hover svg {
    transform: scale(1.1);
    color: #e91e63;
}

/* Category Icon Animations */
.category-icon {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-card:hover .category-icon {
    transform: scale(1.1) rotate(5deg);
    animation: float 2s ease-in-out infinite;
}

.category-icon svg {
    transition: all 0.3s ease;
}

.category-card:hover .category-icon svg {
    transform: scale(1.2);
}

/* Button Icon Animations */
.btn-icon {
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
}

.btn-icon:hover {
    transform: translateY(-2px);
}

.btn-icon svg {
    transition: all 0.3s ease;
}

.btn-icon:hover svg {
    transform: translateX(3px);
}

/* Loading Icon Animation */
.loading-icon {
    animation: spin 1s linear infinite;
}

/* Success Icon Animation */
.success-icon {
    animation: bounceIn 0.6s ease-out;
}

/* Error Icon Animation */
.error-icon {
    animation: wiggle 0.8s ease-in-out;
}

/* Social Media Icon Animations */
.social-icon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
}

.social-icon:hover {
    transform: scale(1.2) rotate(10deg);
    animation: float 2s ease-in-out infinite;
}

/* Search Icon Animation */
.search-icon {
    transition: all 0.3s ease;
}

.search-icon:hover {
    transform: scale(1.1);
    animation: rubberBand 0.8s ease-out;
}

/* Close Icon Animation */
.close-icon {
    transition: all 0.3s ease;
}

.close-icon:hover {
    transform: scale(1.1) rotate(90deg);
}

/* Arrow Icon Animations */
.arrow-icon {
    transition: all 0.3s ease;
}

.arrow-icon:hover {
    transform: translateX(5px);
}

.arrow-up:hover {
    transform: translateY(-5px);
}

.arrow-down:hover {
    transform: translateY(5px);
}

/* Heart Icon Animation */
.heart-icon {
    transition: all 0.3s ease;
}

.heart-icon:hover {
    transform: scale(1.2);
    animation: heartbeat 1s ease-in-out;
    color: #e91e63;
}

/* Star Icon Animation */
.star-icon {
    transition: all 0.3s ease;
}

.star-icon:hover {
    transform: scale(1.1) rotate(72deg);
    color: #fbbf24;
}

/* Notification Icon Animation */
.notification-icon {
    position: relative;
}

.notification-icon::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 8px;
    height: 8px;
    background: #e91e63;
    border-radius: 50%;
    animation: heartbeat 2s ease-in-out infinite;
}

/* Icon Glow Effect */
.icon-glow:hover {
    filter: drop-shadow(0 0 8px rgba(233, 30, 99, 0.6));
}

/* Icon Shake Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
    20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.icon-shake:hover {
    animation: shake 0.5s ease-in-out;
}

/* Performance optimizations */
* {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
    -webkit-perspective: 1000;
    perspective: 1000;
}

/* Smooth transitions for all interactive elements */
button, a, svg, .icon-hover, .nav-link, .mobile-nav-link {
    will-change: transform;
    transform: translateZ(0);
}

/* Enhanced focus states for accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible {
    outline: 2px solid #e91e63;
    outline-offset: 2px;
    border-radius: 4px;
}

/* Improved animation performance */
@media (prefers-reduced-motion: no-preference) {
    .animate-fade-in-up,
    .animate-bounce-in,
    .animate-slide-in-right,
    .animate-slide-in-left,
    .animate-zoom-in {
        will-change: transform, opacity;
    }
}

/* Loading state improvements */
.loading-state {
    pointer-events: none;
    opacity: 0.7;
}

.loading-state::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #e91e63;
    border-top: 2px solid transparent;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced card hover effects */
.card-shadow {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-shadow:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Improved gradient animations */
.bg-gradient-to-r,
.bg-gradient-to-br {
    background-size: 200% 200%;
    animation: gradientShift 3s ease infinite;
}

@keyframes gradientShift {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

/* Enhanced notification animations */
.notification-enter {
    animation: slideInRight 0.3s ease-out;
}

.notification-exit {
    animation: slideOutRight 0.3s ease-in;
}

@keyframes slideOutRight {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Micro-interactions for better UX */
.micro-bounce:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.micro-bounce {
    transition: transform 0.1s ease;
}

/* Enhanced mobile responsiveness */
@media (max-width: 640px) {
    .animate-fade-in-up {
        animation-duration: 0.6s;
    }

    .card-shadow:hover {
        transform: translateY(-4px) scale(1.01);
    }

    .icon-hover:hover {
        transform: scale(1.05);
    }
}

/* Dark mode support preparation */
@media (prefers-color-scheme: dark) {
    .icon-glow:hover {
        filter: drop-shadow(0 0 8px rgba(233, 30, 99, 0.8));
    }
}

/* Print styles */
@media print {
    .animate-fade-in-up,
    .animate-bounce-in,
    .animate-slide-in-right,
    .animate-slide-in-left,
    .animate-zoom-in,
    .icon-hover,
    .card-shadow {
        animation: none !important;
        transition: none !important;
        transform: none !important;
    }
}
