<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Admin Dashboard - Amazora{% endblock %}</title>
    <link rel="icon" type="image/svg+xml" href="{{ url_for('static', filename='images/favicon.svg') }}">
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        body { font-family: 'Inter', sans-serif; }
        .sidebar-link:hover { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); }
        .admin-card { transition: all 0.3s ease; }
        .admin-card:hover { transform: translateY(-2px); box-shadow: 0 10px 25px rgba(0,0,0,0.1); }
    </style>
</head>
<body class="bg-gray-100">
    <div class="flex h-screen">
        <!-- Sidebar -->
        <div class="w-64 bg-gradient-to-b from-indigo-900 to-purple-900 text-white shadow-xl">
            <div class="p-6">
                <div class="flex items-center space-x-3">
                    <img src="{{ url_for('static', filename='images/logo.png') }}" alt="Amazora" class="h-10 w-auto">
                    <h1 class="text-xl font-bold">Admin Panel</h1>
                </div>
            </div>
            
            <nav class="mt-8">
                <a href="{{ url_for('admin_dashboard') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200 {% if request.endpoint == 'admin_dashboard' %}bg-indigo-700{% endif %}">
                    <i class="fas fa-tachometer-alt mr-3"></i>
                    Dashboard
                </a>
                <a href="{{ url_for('admin_users') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200 {% if request.endpoint == 'admin_users' %}bg-indigo-700{% endif %}">
                    <i class="fas fa-users mr-3"></i>
                    Users
                </a>
                <a href="{{ url_for('admin_categories') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200 {% if request.endpoint == 'admin_categories' %}bg-indigo-700{% endif %}">
                    <i class="fas fa-tags mr-3"></i>
                    Categories
                </a>
                <a href="{{ url_for('admin_products') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200 {% if request.endpoint == 'admin_products' %}bg-indigo-700{% endif %}">
                    <i class="fas fa-box mr-3"></i>
                    Products
                </a>
                <a href="{{ url_for('admin_orders') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200 {% if request.endpoint in ['admin_orders', 'admin_order_detail'] %}bg-indigo-700{% endif %}">
                    <i class="fas fa-receipt mr-3"></i>
                    Orders
                </a>
                <div class="border-t border-indigo-700 mt-6 pt-6">
                    <a href="{{ url_for('home') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-indigo-700 transition-colors duration-200">
                        <i class="fas fa-home mr-3"></i>
                        Back to Site
                    </a>
                    <a href="{{ url_for('logout') }}" class="sidebar-link flex items-center px-6 py-3 text-white hover:bg-red-600 transition-colors duration-200">
                        <i class="fas fa-sign-out-alt mr-3"></i>
                        Logout
                    </a>
                </div>
            </nav>
        </div>

        <!-- Main Content -->
        <div class="flex-1 flex flex-col overflow-hidden">
            <!-- Header -->
            <header class="bg-white shadow-sm border-b border-gray-200">
                <div class="flex items-center justify-between px-6 py-4">
                    <h2 class="text-2xl font-semibold text-gray-800">{% block page_title %}Dashboard{% endblock %}</h2>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-600">Welcome, {{ current_user.username }}</span>
                        <div class="w-8 h-8 bg-gradient-to-r from-indigo-500 to-purple-600 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                    </div>
                </div>
            </header>

            <!-- Content -->
            <main class="flex-1 overflow-y-auto p-6">
                <!-- Flash Messages -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        <div class="mb-6">
                            {% for message in messages %}
                                <div class="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-2 flex items-center">
                                    <i class="fas fa-check-circle mr-2"></i>
                                    {{ message }}
                                </div>
                            {% endfor %}
                        </div>
                    {% endif %}
                {% endwith %}

                {% block content %}{% endblock %}
            </main>
        </div>
    </div>

    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script>
        // Confirm delete actions
        function confirmDelete(message) {
            return confirm(message || 'Are you sure you want to delete this item?');
        }
    </script>
</body>
</html>
