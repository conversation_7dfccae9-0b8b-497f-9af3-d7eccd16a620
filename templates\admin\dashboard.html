{% extends "admin/base.html" %}

{% block title %}Admin Dashboard - Amazora{% endblock %}
{% block page_title %}Dashboard Overview{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Users -->
    <div class="admin-card bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-blue-100 text-sm font-medium">Total Users</p>
                <p class="text-3xl font-bold">{{ total_users }}</p>
            </div>
            <div class="bg-blue-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-users text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Total Products -->
    <div class="admin-card bg-gradient-to-r from-green-500 to-green-600 text-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-green-100 text-sm font-medium">Total Products</p>
                <p class="text-3xl font-bold">{{ total_products }}</p>
            </div>
            <div class="bg-green-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-box text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Total Categories -->
    <div class="admin-card bg-gradient-to-r from-purple-500 to-purple-600 text-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-purple-100 text-sm font-medium">Categories</p>
                <p class="text-3xl font-bold">{{ total_categories }}</p>
            </div>
            <div class="bg-purple-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-tags text-2xl"></i>
            </div>
        </div>
    </div>

    <!-- Total Orders -->
    <div class="admin-card bg-gradient-to-r from-orange-500 to-orange-600 text-white rounded-xl p-6 shadow-lg">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-orange-100 text-sm font-medium">Total Orders</p>
                <p class="text-3xl font-bold">{{ total_orders }}</p>
            </div>
            <div class="bg-orange-400 bg-opacity-30 rounded-full p-3">
                <i class="fas fa-shopping-cart text-2xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
        <div class="space-y-3">
            <a href="{{ url_for('admin_add_product') }}" class="flex items-center p-3 bg-blue-50 hover:bg-blue-100 rounded-lg transition-colors duration-200">
                <i class="fas fa-plus-circle text-blue-600 mr-3"></i>
                <span class="text-blue-800 font-medium">Add New Product</span>
            </a>
            <a href="{{ url_for('admin_add_category') }}" class="flex items-center p-3 bg-green-50 hover:bg-green-100 rounded-lg transition-colors duration-200">
                <i class="fas fa-plus-circle text-green-600 mr-3"></i>
                <span class="text-green-800 font-medium">Add New Category</span>
            </a>
            <a href="{{ url_for('admin_orders') }}" class="flex items-center p-3 bg-indigo-50 hover:bg-indigo-100 rounded-lg transition-colors duration-200">
                <i class="fas fa-receipt text-indigo-600 mr-3"></i>
                <span class="text-indigo-800 font-medium">View Orders</span>
            </a>
            <a href="{{ url_for('admin_users') }}" class="flex items-center p-3 bg-purple-50 hover:bg-purple-100 rounded-lg transition-colors duration-200">
                <i class="fas fa-users text-purple-600 mr-3"></i>
                <span class="text-purple-800 font-medium">Manage Users</span>
            </a>
        </div>
    </div>

    <!-- Recent Users -->
    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Users</h3>
        <div class="space-y-3">
            {% for user in recent_users %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">{{ user.username }}</p>
                    <p class="text-sm text-gray-600">{{ user.email }}</p>
                </div>
                <span class="text-xs text-gray-500">{{ user.created_at.strftime('%m/%d') }}</span>
            </div>
            {% endfor %}
        </div>
    </div>

    <!-- Recent Products -->
    <div class="admin-card bg-white rounded-xl p-6 shadow-lg">
        <h3 class="text-lg font-semibold text-gray-800 mb-4">Recent Products</h3>
        <div class="space-y-3">
            {% for product in recent_products %}
            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div>
                    <p class="font-medium text-gray-800">{{ product.name }}</p>
                    <p class="text-sm text-gray-600">${{ "%.2f"|format(product.price) }}</p>
                </div>
                <span class="text-xs text-gray-500">{{ product.created_at.strftime('%m/%d') }}</span>
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- System Status -->
<div class="admin-card bg-white rounded-xl p-6 shadow-lg">
    <h3 class="text-lg font-semibold text-gray-800 mb-4">System Status</h3>
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="flex items-center p-4 bg-green-50 rounded-lg">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="text-green-800 font-medium">Database Connected</span>
        </div>
        <div class="flex items-center p-4 bg-green-50 rounded-lg">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="text-green-800 font-medium">Cache Active</span>
        </div>
        <div class="flex items-center p-4 bg-green-50 rounded-lg">
            <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
            <span class="text-green-800 font-medium">All Systems Operational</span>
        </div>
    </div>
</div>
{% endblock %}
