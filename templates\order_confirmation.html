{% extends "base.html" %}

{% block title %}Order Confirmation - Amazora{% endblock %}

{% block content %}
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Success Message -->
    <div class="text-center mb-8">
        <div class="w-20 h-20 bg-gradient-to-r from-green-400 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <i class="fas fa-check text-white text-3xl"></i>
        </div>
        <h1 class="text-4xl font-bold text-gray-900 mb-3">🎉 Order Successful!</h1>
        <p class="text-lg text-gray-600 mb-4">Thank you for your purchase. Your order has been successfully placed and confirmed.</p>
        <div class="inline-flex items-center px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-medium">
            <i class="fas fa-envelope mr-2"></i>
            Confirmation email sent to your inbox
        </div>
    </div>

    <!-- Order Details -->
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-xl font-semibold text-gray-900">Order Details</h2>
            <div class="text-right">
                <p class="text-sm text-gray-600">Order #{{ order.id }}</p>
                <p class="text-sm text-gray-600">{{ order.created_at.strftime('%B %d, %Y at %I:%M %p') }}</p>
            </div>
        </div>

        <!-- Order Status and Payment Method -->
        <div class="mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-2">
                    {% if order.status == 'pending' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-yellow-100 text-yellow-800">
                            <i class="fas fa-clock mr-1"></i>
                            Pending
                        </span>
                    {% elif order.status == 'confirmed' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-check mr-1"></i>
                            Confirmed
                        </span>
                    {% elif order.status == 'shipped' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                            <i class="fas fa-truck mr-1"></i>
                            Shipped
                        </span>
                    {% elif order.status == 'delivered' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <i class="fas fa-check-circle mr-1"></i>
                            Delivered
                        </span>
                    {% elif order.status == 'cancelled' %}
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <i class="fas fa-times-circle mr-1"></i>
                            Cancelled
                        </span>
                    {% endif %}
                </div>
                
                <div class="text-right">
                    <p class="text-sm text-gray-600">Payment Method</p>
                    {% if order.payment_method == 'instapay' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            <i class="fas fa-credit-card mr-1"></i>
                            Instapay
                        </span>
                    {% elif order.payment_method == 'pay_after_shipping' %}
                        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                            <i class="fas fa-truck mr-1"></i>
                            Pay After Shipping
                        </span>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Order Items -->
        <div class="space-y-4 mb-6">
            <h3 class="text-lg font-semibold text-gray-900">Items Ordered</h3>
            {% for item in order.items %}
            <div class="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                    {% if item.product.image_url %}
                        <img src="{{ item.product.image_url | image_url }}" alt="{{ item.product.name }}" class="w-full h-full object-cover">
                    {% else %}
                        <div class="w-full h-full bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
                            <i class="fas fa-image text-gray-500"></i>
                        </div>
                    {% endif %}
                </div>
                <div class="flex-1 min-w-0">
                    <h4 class="text-sm font-medium text-gray-900 truncate">{{ item.product.name }}</h4>
                    <p class="text-sm text-gray-500">Qty: {{ item.quantity }}</p>
                </div>
                <div class="text-right">
                    <p class="text-sm font-medium text-gray-900">${{ "%.2f"|format(item.price * item.quantity) }}</p>
                    <p class="text-xs text-gray-500">${{ "%.2f"|format(item.price) }} each</p>
                </div>
            </div>
            {% endfor %}
        </div>

        <!-- Order Summary -->
        <div class="border-t border-gray-200 pt-6">
            <div class="space-y-2">
                <div class="flex justify-between text-sm text-gray-600">
                    <span>Subtotal</span>
                    <span>${{ "%.2f"|format(order.total_amount) }}</span>
                </div>
                <div class="flex justify-between text-sm text-gray-600">
                    <span>Shipping</span>
                    <span>Free</span>
                </div>
                <div class="border-t border-gray-200 pt-2">
                    <div class="flex justify-between text-lg font-bold text-gray-900">
                        <span>Total</span>
                        <span>${{ "%.2f"|format(order.total_amount) }}</span>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Shipping Information -->
    {% if order.shipping_address %}
    <div class="bg-white border border-gray-200 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
        <div class="text-gray-700">
            <p>{{ order.shipping_address }}</p>
        </div>
    </div>
    {% endif %}

    <!-- Next Steps -->
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
        <h3 class="text-lg font-semibold text-blue-900 mb-4">What's Next?</h3>
        <div class="space-y-3">
            <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span class="text-white text-xs font-bold">1</span>
                </div>
                <div>
                    <p class="text-sm font-medium text-blue-900">Order Confirmation Email</p>
                    <p class="text-sm text-blue-700">You'll receive a confirmation email shortly with your order details.</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span class="text-white text-xs font-bold">2</span>
                </div>
                <div>
                    <p class="text-sm font-medium text-blue-900">Order Processing</p>
                    <p class="text-sm text-blue-700">We'll process your order and prepare it for shipping within 1-2 business days.</p>
                </div>
            </div>
            <div class="flex items-start space-x-3">
                <div class="w-6 h-6 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                    <span class="text-white text-xs font-bold">3</span>
                </div>
                <div>
                    <p class="text-sm font-medium text-blue-900">Shipping Updates</p>
                    <p class="text-sm text-blue-700">You'll receive tracking information once your order ships.</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="text-center">
        <div class="mb-6">
            <h3 class="text-xl font-semibold text-gray-900 mb-2">What would you like to do next?</h3>
            <p class="text-gray-600">Continue shopping or view your order details</p>
        </div>
        
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-4 sm:space-y-0 sm:space-x-6">
            <a href="{{ url_for('home') }}" class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-semibold rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1">
                <i class="fas fa-shopping-cart mr-3 text-lg"></i>
                Continue Shopping
            </a>
            <a href="{{ url_for('user_profile') }}" class="inline-flex items-center px-6 py-3 border-2 border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 hover:border-gray-400 transition-all duration-200">
                <i class="fas fa-user mr-2"></i>
                View My Orders
            </a>
        </div>
        
        <div class="mt-8 p-4 bg-blue-50 rounded-lg">
            <div class="flex items-center justify-center space-x-2 text-blue-800">
                <i class="fas fa-info-circle"></i>
                <span class="text-sm">Your order confirmation has been sent to your email</span>
            </div>
        </div>
    </div>

    <!-- Help Section -->
    <div class="mt-12 text-center">
        <p class="text-sm text-gray-600 mb-4">Need help with your order?</p>
        <div class="flex flex-col sm:flex-row items-center justify-center space-y-2 sm:space-y-0 sm:space-x-6">
            <a href="#" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                <i class="fas fa-envelope mr-1"></i>
                Contact Support
            </a>
            <a href="#" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                <i class="fas fa-question-circle mr-1"></i>
                FAQ
            </a>
            <a href="#" class="text-indigo-600 hover:text-indigo-800 text-sm font-medium">
                <i class="fas fa-phone mr-1"></i>
                Call Us
            </a>
        </div>
    </div>
</div>

<script>
// Auto-redirect to profile after 10 seconds
setTimeout(function() {
    if (confirm('Would you like to view your order history?')) {
        window.location.href = "{{ url_for('user_profile') }}";
    }
}, 10000);
</script>
{% endblock %}
