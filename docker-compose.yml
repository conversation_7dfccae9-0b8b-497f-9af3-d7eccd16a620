version: '3.8'

services:
  web:
    build: .
    ports:
      - "8000:8000"
    environment:
      - FLASK_ENV=production
      - SECRET_KEY=your-production-secret-key-here
      - DATABASE_URL=sqlite:///amazora.db
    volumes:
      - ./static/uploads:/app/static/uploads
      - ./instance:/app/instance
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:8000/', timeout=10)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
