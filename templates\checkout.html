{% extends "base.html" %}

{% block title %}Checkout - Amazora{% endblock %}

{% block content %}
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
    <!-- Breadcrumb -->
    <nav class="flex mb-8" aria-label="Breadcrumb">
        <ol class="inline-flex items-center space-x-1 md:space-x-3">
            <li class="inline-flex items-center">
                <a href="{{ url_for('home') }}" class="text-gray-700 hover:text-indigo-600">Home</a>
            </li>
            <li>
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <a href="{{ url_for('cart') }}" class="ml-1 text-gray-700 hover:text-indigo-600 md:ml-2">Cart</a>
                </div>
            </li>
            <li aria-current="page">
                <div class="flex items-center">
                    <svg class="w-6 h-6 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd"></path>
                    </svg>
                    <span class="ml-1 text-gray-500 md:ml-2">Checkout</span>
                </div>
            </li>
        </ol>
    </nav>

    <div class="lg:grid lg:grid-cols-2 lg:gap-x-12 xl:gap-x-16">
        <!-- Order Summary -->
        <div class="lg:col-span-1">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Order Summary</h2>
            
            <div class="bg-white border border-gray-200 rounded-lg p-6">
                <div class="space-y-4">
                    {% for item in cart_items %}
                    <div class="flex items-center space-x-4">
                        <div class="w-16 h-16 bg-gray-200 rounded-lg overflow-hidden flex-shrink-0">
                            {% if item.product.image_url %}
                                <img src="{{ item.product.image_url | image_url }}" alt="{{ item.product.name }}" class="w-full h-full object-cover">
                            {% else %}
                                <div class="w-full h-full bg-gradient-to-r from-gray-300 to-gray-400 flex items-center justify-center">
                                    <i class="fas fa-image text-gray-500"></i>
                                </div>
                            {% endif %}
                        </div>
                        <div class="flex-1 min-w-0">
                            <h3 class="text-sm font-medium text-gray-900 truncate">{{ item.product.name }}</h3>
                            <p class="text-sm text-gray-500">Qty: {{ item.quantity }}</p>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-gray-900">${{ "%.2f"|format(item.product.price * item.quantity) }}</p>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                
                <div class="border-t border-gray-200 mt-6 pt-6">
                    <div class="flex justify-between text-base font-medium text-gray-900">
                        <p>Subtotal</p>
                        <p>${{ "%.2f"|format(total) }}</p>
                    </div>
                    <div class="flex justify-between text-sm text-gray-500 mt-2">
                        <p>Shipping</p>
                        <p>Free</p>
                    </div>
                    <div class="flex justify-between text-lg font-bold text-gray-900 mt-4 pt-4 border-t border-gray-200">
                        <p>Total</p>
                        <p>${{ "%.2f"|format(total) }}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Checkout Form -->
        <div class="lg:col-span-1 mt-10 lg:mt-0">
            <h2 class="text-2xl font-bold text-gray-900 mb-8">Shipping Information</h2>
            
            <form method="POST" class="space-y-6" id="checkout-form">
                <!-- Personal Information -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Personal Information</h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label for="first_name" class="block text-sm font-medium text-gray-700 mb-2">First Name</label>
                            <input type="text" 
                                   id="first_name" 
                                   name="first_name" 
                                   value="{{ current_user.profile.first_name if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                        <div>
                            <label for="last_name" class="block text-sm font-medium text-gray-700 mb-2">Last Name</label>
                            <input type="text" 
                                   id="last_name" 
                                   name="last_name" 
                                   value="{{ current_user.profile.last_name if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                        <input type="email" 
                               id="email" 
                               name="email" 
                               value="{{ current_user.email }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               required>
                    </div>
                    
                    <div class="mt-4">
                        <label for="phone" class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <input type="tel" 
                               id="phone" 
                               name="phone" 
                               value="{{ current_user.profile.phone if current_user.profile else '' }}"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                               required>
                    </div>
                </div>

                <!-- Shipping Address -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Shipping Address</h3>
                    
                    <div class="mb-4">
                        <label for="shipping_address" class="block text-sm font-medium text-gray-700 mb-2">Street Address</label>
                        <textarea id="shipping_address" 
                                  name="shipping_address" 
                                  rows="3"
                                  class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                  placeholder="Enter your complete shipping address"
                                  required>{{ current_user.profile.address if current_user.profile else '' }}</textarea>
                    </div>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div>
                            <label for="city" class="block text-sm font-medium text-gray-700 mb-2">City</label>
                            <input type="text" 
                                   id="city" 
                                   name="city" 
                                   value="{{ current_user.profile.city if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                        <div>
                            <label for="state" class="block text-sm font-medium text-gray-700 mb-2">State/Province</label>
                            <input type="text" 
                                   id="state" 
                                   name="state" 
                                   value="{{ current_user.profile.state if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                        <div>
                            <label for="zip_code" class="block text-sm font-medium text-gray-700 mb-2">ZIP/Postal Code</label>
                            <input type="text" 
                                   id="zip_code" 
                                   name="zip_code" 
                                   value="{{ current_user.profile.zip_code if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                        <div>
                            <label for="country" class="block text-sm font-medium text-gray-700 mb-2">Country</label>
                            <input type="text" 
                                   id="country" 
                                   name="country" 
                                   value="{{ current_user.profile.country if current_user.profile else '' }}"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                   required>
                        </div>
                    </div>
                </div>

                <!-- Payment Method -->
                <div class="bg-white border border-gray-200 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Payment Method</h3>
                    
                    <div class="space-y-4">
                        <!-- Instapay Option -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition-colors duration-200">
                            <label class="flex items-start space-x-3 cursor-pointer">
                                <input type="radio" 
                                       name="payment_method" 
                                       value="instapay" 
                                       class="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                       required>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-credit-card text-indigo-600 text-lg"></i>
                                        <span class="font-medium text-gray-900">Instapay</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            <i class="fas fa-bolt mr-1"></i>
                                            Instant
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Pay instantly with your credit/debit card</p>
                                </div>
                            </label>
                        </div>

                        <!-- Pay After Shipping Option -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:border-indigo-300 transition-colors duration-200">
                            <label class="flex items-start space-x-3 cursor-pointer">
                                <input type="radio" 
                                       name="payment_method" 
                                       value="pay_after_shipping" 
                                       class="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300"
                                       required>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <i class="fas fa-truck text-blue-600 text-lg"></i>
                                        <span class="font-medium text-gray-900">Pay After Shipping</span>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                            <i class="fas fa-clock mr-1"></i>
                                            Cash on Delivery
                                        </span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Pay when you receive your order</p>
                                </div>
                            </label>
                        </div>
                    </div>

                    <!-- Instapay Payment Details (Hidden by default) -->
                    <div id="instapay-details" class="mt-6 p-4 bg-gray-50 rounded-lg" style="display: none;">
                        <h4 class="font-medium text-gray-900 mb-3">Payment Details</h4>
                        <div class="space-y-3">
                            <div>
                                <label for="card_number" class="block text-sm font-medium text-gray-700 mb-1">Card Number</label>
                                <input type="text" 
                                       id="card_number" 
                                       name="card_number" 
                                       class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                       placeholder="1234 5678 9012 3456">
                            </div>
                            
                            <div class="grid grid-cols-3 gap-3">
                                <div>
                                    <label for="expiry" class="block text-sm font-medium text-gray-700 mb-1">Expiry Date</label>
                                    <input type="text" 
                                           id="expiry" 
                                           name="expiry" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                           placeholder="MM/YY">
                                </div>
                                <div>
                                    <label for="cvv" class="block text-sm font-medium text-gray-700 mb-1">CVV</label>
                                    <input type="text" 
                                           id="cvv" 
                                           name="cvv" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                           placeholder="123">
                                </div>
                                <div>
                                    <label for="card_name" class="block text-sm font-medium text-gray-700 mb-1">Name on Card</label>
                                    <input type="text" 
                                           id="card_name" 
                                           name="card_name" 
                                           class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-indigo-500 transition-colors duration-200"
                                           placeholder="John Doe">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                            <div class="flex items-start">
                                <i class="fas fa-shield-alt text-blue-600 mt-1 mr-2"></i>
                                <div class="text-xs text-blue-800">
                                    <p class="font-medium">Secure Payment</p>
                                    <p>Your payment information is encrypted and secure.</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Pay After Shipping Info -->
                    <div id="pay-after-shipping-info" class="mt-6 p-4 bg-blue-50 rounded-lg" style="display: none;">
                        <div class="flex items-start">
                            <i class="fas fa-info-circle text-blue-600 mt-1 mr-3"></i>
                            <div class="text-sm text-blue-800">
                                <p class="font-medium">Pay After Shipping</p>
                                <p>You'll pay when you receive your order. No payment required now.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Order Summary -->
                <div class="bg-gray-50 rounded-lg p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>
                    
                    <div class="space-y-2">
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Subtotal ({{ cart_items|length }} items)</span>
                            <span>${{ "%.2f"|format(total) }}</span>
                        </div>
                        <div class="flex justify-between text-sm text-gray-600">
                            <span>Shipping</span>
                            <span>Free</span>
                        </div>
                        <div class="border-t border-gray-200 pt-2 mt-2">
                            <div class="flex justify-between text-lg font-bold text-gray-900">
                                <span>Total</span>
                                <span>${{ "%.2f"|format(total) }}</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Terms and Conditions -->
                <div class="flex items-start space-x-3">
                    <input type="checkbox" 
                           id="terms" 
                           name="terms" 
                           class="mt-1 h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded"
                           required>
                    <label for="terms" class="text-sm text-gray-700">
                        I agree to the <a href="#" class="text-indigo-600 hover:text-indigo-800">Terms and Conditions</a> and <a href="#" class="text-indigo-600 hover:text-indigo-800">Privacy Policy</a>
                    </label>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-between pt-6">
                    <a href="{{ url_for('cart') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 font-medium rounded-lg hover:bg-gray-50 transition-colors duration-200">
                        <i class="fas fa-arrow-left mr-2"></i>
                        Back to Cart
                    </a>
                    <button type="submit" class="inline-flex items-center px-8 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white font-medium rounded-lg hover:from-indigo-700 hover:to-purple-700 transition-all duration-200 shadow-lg">
                        <i class="fas fa-lock mr-2"></i>
                        Place Order
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
// Checkout form handling
document.addEventListener('DOMContentLoaded', function() {
    const checkoutForm = document.getElementById('checkout-form');
    const submitButton = checkoutForm.querySelector('button[type="submit"]');
    
    // Prevent form from being reset unexpectedly
    checkoutForm.addEventListener('submit', function(e) {
        // Show loading state
        submitButton.disabled = true;
        submitButton.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Processing...';
        
        // Validate required fields
        const requiredFields = checkoutForm.querySelectorAll('[required]');
        let isValid = true;
        
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                isValid = false;
                field.classList.add('border-red-500');
            } else {
                field.classList.remove('border-red-500');
            }
        });
        
        // Check if payment method is selected
        const paymentMethod = document.querySelector('input[name="payment_method"]:checked');
        if (!paymentMethod) {
            isValid = false;
            alert('Please select a payment method');
            e.preventDefault();
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock mr-2"></i>Place Order';
            return;
        }
        
        // Check terms agreement
        const termsCheckbox = document.getElementById('terms');
        if (!termsCheckbox.checked) {
            isValid = false;
            alert('Please agree to the Terms and Conditions');
            e.preventDefault();
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock mr-2"></i>Place Order';
            return;
        }
        
        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields');
            submitButton.disabled = false;
            submitButton.innerHTML = '<i class="fas fa-lock mr-2"></i>Place Order';
            return;
        }
        
        // Form is valid, allow submission
        console.log('Form submitted successfully');
    });
    
    // Auto-fill shipping address from profile
    // Combine address fields into shipping address
    const address = document.getElementById('shipping_address');
    const city = document.getElementById('city');
    const state = document.getElementById('state');
    const zipCode = document.getElementById('zip_code');
    const country = document.getElementById('country');
    
    function updateShippingAddress() {
        const addressParts = [];
        if (address.value) addressParts.push(address.value);
        if (city.value) addressParts.push(city.value);
        if (state.value) addressParts.push(state.value);
        if (zipCode.value) addressParts.push(zipCode.value);
        if (country.value) addressParts.push(country.value);
        
        if (addressParts.length > 0) {
            // Don't modify the original address field, create a hidden field instead
            let hiddenAddress = document.getElementById('combined_shipping_address');
            if (!hiddenAddress) {
                hiddenAddress = document.createElement('input');
                hiddenAddress.type = 'hidden';
                hiddenAddress.id = 'combined_shipping_address';
                hiddenAddress.name = 'combined_shipping_address';
                checkoutForm.appendChild(hiddenAddress);
            }
            hiddenAddress.value = addressParts.join(', ');
        }
    }
    
    // Update shipping address when any field changes
    [city, state, zipCode, country].forEach(field => {
        field.addEventListener('blur', updateShippingAddress);
    });
    
    // Payment method selection
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    const instapayDetails = document.getElementById('instapay-details');
    const payAfterShippingInfo = document.getElementById('pay-after-shipping-info');
    
    function handlePaymentMethodChange() {
        const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
        
        if (selectedMethod) {
            if (selectedMethod.value === 'instapay') {
                instapayDetails.style.display = 'block';
                payAfterShippingInfo.style.display = 'none';
                
                // Make card fields required
                document.getElementById('card_number').required = true;
                document.getElementById('expiry').required = true;
                document.getElementById('cvv').required = true;
                document.getElementById('card_name').required = true;
            } else if (selectedMethod.value === 'pay_after_shipping') {
                instapayDetails.style.display = 'none';
                payAfterShippingInfo.style.display = 'block';
                
                // Remove required from card fields
                document.getElementById('card_number').required = false;
                document.getElementById('expiry').required = false;
                document.getElementById('cvv').required = false;
                document.getElementById('card_name').required = false;
            }
        }
    }
    
    // Add event listeners to payment method radio buttons
    paymentMethods.forEach(method => {
        method.addEventListener('change', handlePaymentMethodChange);
    });
    
    // Initialize payment method display
    handlePaymentMethodChange();
});

// Card number formatting
document.getElementById('card_number').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\s+/g, '').replace(/[^0-9]/gi, '');
    let formattedValue = value.match(/.{1,4}/g)?.join(' ') || value;
    e.target.value = formattedValue;
});

// Expiry date formatting
document.getElementById('expiry').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    if (value.length >= 2) {
        value = value.slice(0, 2) + '/' + value.slice(2, 4);
    }
    e.target.value = value;
});

// CVV formatting
document.getElementById('cvv').addEventListener('input', function(e) {
    let value = e.target.value.replace(/\D/g, '');
    e.target.value = value.slice(0, 4);
});
</script>
{% endblock %}
